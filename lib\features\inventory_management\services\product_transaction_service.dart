// بسم الله الرحمن الرحيم
// خدمة معاملات المنتج - مسؤولة عن تتبع معاملات البيع والشراء للمنتجات

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:intl/intl.dart';

import '../models/product_transaction_model.dart';
import '../../../constant.dart';

/// خدمة معاملات المنتج
class ProductTransactionService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة معاملات المنتج
  static final ProductTransactionService _instance = ProductTransactionService._internal();
  factory ProductTransactionService() => _instance;
  ProductTransactionService._internal();

  // مثيل Firebase Database
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  /// الحصول على معاملات البيع لمنتج معين
  Future<List<ProductTransactionModel>> getProductSalesTransactions(String productCode) async {
    try {
      final List<ProductTransactionModel> transactions = [];

      // البحث في معاملات البيع
      final salesSnapshot = await _database
          .ref(constUserId)
          .child('Sales Transition')
          .get();

      if (salesSnapshot.exists && salesSnapshot.value != null) {
        final salesData = salesSnapshot.value as Map<dynamic, dynamic>;

        salesData.forEach((key, value) {
          try {
            final saleData = Map<String, dynamic>.from(value);
            final productList = saleData['productList'] as List<dynamic>?;

            if (productList != null) {
              for (var product in productList) {
                Map<String, dynamic> productMap;

                // التحقق من نوع بيانات المنتج
                if (product is String) {
                  // إذا كان المنتج محفوظ كـ JSON string
                  try {
                    productMap = Map<String, dynamic>.from(jsonDecode(product));
                  } catch (e) {
                    continue; // تخطي هذا المنتج
                  }
                } else if (product is Map) {
                  productMap = Map<String, dynamic>.from(product);
                } else {
                  continue; // تخطي هذا المنتج
                }

                // التحقق من كود المنتج (يمكن أن يكون productCode أو product_id أو uuid)
                final productCodeInData = productMap['productCode'] ??
                                        productMap['product_id'] ??
                                        productMap['uuid'] ?? '';

                if (productCodeInData == productCode) {
                  final quantity = int.tryParse(productMap['quantity'].toString()) ?? 0;
                  final productName = productMap['productName'] ??
                                    productMap['product_name'] ?? '';
                  final subTotal = productMap['subTotal'] ??
                                 productMap['sub_total'] ?? '0';

                  transactions.add(ProductTransactionModel(
                    id: key.toString(),
                    productCode: productCode,
                    productName: productName,
                    transactionType: TransactionType.sale,
                    quantity: quantity,
                    unitPrice: double.tryParse(subTotal.toString()) ?? 0.0,
                    totalAmount: double.tryParse(subTotal.toString()) ?? 0.0,
                    date: DateTime.tryParse(saleData['purchaseDate']) ?? DateTime.now(),
                    invoiceNumber: saleData['invoiceNumber'] ?? '',
                    customerName: saleData['customerName'] ?? '',
                    customerPhone: saleData['customerPhone'] ?? '',
                    paymentType: saleData['paymentType'] ?? '',
                    notes: '',
                  ));
                }
              }
            }
          } catch (e) {
            debugPrint('خطأ في معالجة بيانات البيع: $e');
          }
        });
      }

      return transactions;
    } catch (e) {
      debugPrint('خطأ في الحصول على معاملات البيع للمنتج: $e');
      return [];
    }
  }

  /// الحصول على معاملات الشراء لمنتج معين
  Future<List<ProductTransactionModel>> getProductPurchaseTransactions(String productCode) async {
    try {
      final List<ProductTransactionModel> transactions = [];

      // البحث في معاملات الشراء
      final purchaseSnapshot = await _database
          .ref(constUserId)
          .child('Purchase Transition')
          .get();

      if (purchaseSnapshot.exists && purchaseSnapshot.value != null) {
        final purchaseData = purchaseSnapshot.value as Map<dynamic, dynamic>;

        purchaseData.forEach((key, value) {
          try {
            final purchaseDataMap = Map<String, dynamic>.from(value);
            final productList = purchaseDataMap['productList'] as List<dynamic>?;

            if (productList != null) {
              for (var product in productList) {
                final productMap = Map<String, dynamic>.from(product);
                if (productMap['productCode'] == productCode) {
                  transactions.add(ProductTransactionModel(
                    id: key.toString(),
                    productCode: productCode,
                    productName: productMap['productName'] ?? '',
                    transactionType: TransactionType.purchase,
                    quantity: int.tryParse(productMap['productStock'].toString()) ?? 0,
                    unitPrice: double.tryParse(productMap['productPurchasePrice'].toString()) ?? 0.0,
                    totalAmount: (int.tryParse(productMap['productStock'].toString()) ?? 0) *
                                (double.tryParse(productMap['productPurchasePrice'].toString()) ?? 0.0),
                    date: DateTime.tryParse(purchaseDataMap['purchaseDate']) ?? DateTime.now(),
                    invoiceNumber: purchaseDataMap['invoiceNumber'] ?? '',
                    customerName: purchaseDataMap['customerName'] ?? '',
                    customerPhone: purchaseDataMap['customerPhone'] ?? '',
                    paymentType: purchaseDataMap['paymentType'] ?? '',
                    notes: '',
                  ));
                }
              }
            }
          } catch (e) {
            debugPrint('خطأ في معالجة بيانات الشراء: $e');
          }
        });
      }

      return transactions;
    } catch (e) {
      debugPrint('خطأ في الحصول على معاملات الشراء للمنتج: $e');
      return [];
    }
  }

  /// الحصول على جميع معاملات المنتج (بيع وشراء)
  Future<List<ProductTransactionModel>> getAllProductTransactions(String productCode) async {
    try {
      final salesTransactions = await getProductSalesTransactions(productCode);
      final purchaseTransactions = await getProductPurchaseTransactions(productCode);

      final allTransactions = [...salesTransactions, ...purchaseTransactions];

      // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
      allTransactions.sort((a, b) => b.date.compareTo(a.date));

      return allTransactions;
    } catch (e) {
      debugPrint('خطأ في الحصول على جميع معاملات المنتج: $e');
      return [];
    }
  }

  /// حساب إحصائيات المنتج
  Future<ProductStatistics> getProductStatistics(String productCode) async {
    try {
      final allTransactions = await getAllProductTransactions(productCode);

      int totalSalesQuantity = 0;
      int totalPurchaseQuantity = 0;
      double totalSalesAmount = 0.0;
      double totalPurchaseAmount = 0.0;
      int salesInvoiceCount = 0;
      int purchaseInvoiceCount = 0;

      for (var transaction in allTransactions) {
        if (transaction.transactionType == TransactionType.sale) {
          totalSalesQuantity += transaction.quantity;
          totalSalesAmount += transaction.totalAmount;
          salesInvoiceCount++;
        } else if (transaction.transactionType == TransactionType.purchase) {
          totalPurchaseQuantity += transaction.quantity;
          totalPurchaseAmount += transaction.totalAmount;
          purchaseInvoiceCount++;
        }
      }

      final averageSalePrice = totalSalesQuantity > 0 ? totalSalesAmount / totalSalesQuantity : 0.0;
      final averagePurchasePrice = totalPurchaseQuantity > 0 ? totalPurchaseAmount / totalPurchaseQuantity : 0.0;
      final netProfit = totalSalesAmount - totalPurchaseAmount;
      final profitMargin = totalSalesAmount > 0 ? (netProfit / totalSalesAmount) * 100 : 0.0;

      return ProductStatistics(
        totalSalesQuantity: totalSalesQuantity,
        totalPurchaseQuantity: totalPurchaseQuantity,
        totalSalesAmount: totalSalesAmount,
        totalPurchaseAmount: totalPurchaseAmount,
        salesInvoiceCount: salesInvoiceCount,
        purchaseInvoiceCount: purchaseInvoiceCount,
        averageSalePrice: averageSalePrice,
        averagePurchasePrice: averagePurchasePrice,
        netProfit: netProfit,
        profitMargin: profitMargin,
        turnoverRate: 0.0, // سيتم حسابه لاحقاً
      );
    } catch (e) {
      debugPrint('خطأ في حساب إحصائيات المنتج: $e');
      return ProductStatistics(
        totalSalesQuantity: 0,
        totalPurchaseQuantity: 0,
        totalSalesAmount: 0.0,
        totalPurchaseAmount: 0.0,
        salesInvoiceCount: 0,
        purchaseInvoiceCount: 0,
        averageSalePrice: 0.0,
        averagePurchasePrice: 0.0,
        netProfit: 0.0,
        profitMargin: 0.0,
        turnoverRate: 0.0,
      );
    }
  }

  /// الحصول على معاملات المنتج في فترة زمنية محددة
  Future<List<ProductTransactionModel>> getProductTransactionsByDateRange(
    String productCode,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final allTransactions = await getAllProductTransactions(productCode);

      return allTransactions.where((transaction) {
        return transaction.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
               transaction.date.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على معاملات المنتج في فترة زمنية: $e');
      return [];
    }
  }
}

/// مزود خدمة معاملات المنتج
final productTransactionServiceProvider = Provider<ProductTransactionService>((ref) {
  return ProductTransactionService();
});

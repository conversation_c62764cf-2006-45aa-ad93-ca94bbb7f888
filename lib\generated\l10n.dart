// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Dashboard Overview`
  String get dashBoardOverView {
    return Intl.message(
      'Dashboard Overview',
      name: 'dashBoardOverView',
      desc: '',
      args: [],
    );
  }

  /// `Account Settlement Overview`
  String get accountSettlement {
    return Intl.message(
      'Settlement',
      name: 'accountSettlement',
      desc: '',
      args: [],
    );
  }

  /// `Sale & Purchase Reports`
  String get salesAndPurchaseReports {
    return Intl.message(
      'Sale & Purchase Reports',
      name: 'salesAndPurchaseReports',
      desc: '',
      args: [],
    );
  }

  /// `Acceptance of Terms`
  String get acceptanceOfTerms {
    return Intl.message(
      'Acceptance of Terms',
      name: 'acceptanceOfTerms',
      desc: '',
      args: [],
    );
  }

  /// `By accessing or using the Point of Sale (POS) System (the "System") provided by [Your Company Name] ("Company"), you agree to be bound by these Terms of Use. If you do not agree to these Terms of Use, do not use the System.`
  String get byAccessingOrUsingThePointOfSales {
    return Intl.message(
      'By accessing or using the Point of Sale (POS) System (the "System") provided by [Your Company Name] ("Company"), you agree to be bound by these Terms of Use. If you do not agree to these Terms of Use, do not use the System.',
      name: 'byAccessingOrUsingThePointOfSales',
      desc: '',
      args: [],
    );
  }

  /// `Use of the system`
  String get useOfTheSystem {
    return Intl.message(
      'Use of the system',
      name: 'useOfTheSystem',
      desc: '',
      args: [],
    );
  }

  /// `(a) The System is provided solely for the\npurpose of facilitating point of sale\ntransactions andrelatedactivities in your\nbusiness.`
  String get aTheSystemIsProvided {
    return Intl.message(
      '(a) The System is provided solely for the\npurpose of facilitating point of sale\ntransactions andrelatedactivities in your\nbusiness.',
      name: 'aTheSystemIsProvided',
      desc: '',
      args: [],
    );
  }

  /// `(b) You must be at least 18 years old or the\nlegal age of majority in your jurisdiction to\nuse the System.`
  String get bYouMustBeAtLeastYearsOld {
    return Intl.message(
      '(b) You must be at least 18 years old or the\nlegal age of majority in your jurisdiction to\nuse the System.',
      name: 'bYouMustBeAtLeastYearsOld',
      desc: '',
      args: [],
    );
  }

  /// `(c) You are responsible for ensuring that your\naccess to and use of the System is in\ncompliance with all applicable laws and\nregulations.`
  String get cYouHaveResponsiveForEnsuring {
    return Intl.message(
      '(c) You are responsible for ensuring that your\naccess to and use of the System is in\ncompliance with all applicable laws and\nregulations.',
      name: 'cYouHaveResponsiveForEnsuring',
      desc: '',
      args: [],
    );
  }

  /// `Account Registration`
  String get accountRegistration {
    return Intl.message(
      'Account Registration',
      name: 'accountRegistration',
      desc: '',
      args: [],
    );
  }

  /// `(a) To use the System, you may be\nrequired to create an account. You\nagree to provide accurate, current, and\ncomplete information during\nthe registration process and toupdate\nsuchinformationto keep itaccurate\nand complete.`
  String get aToUseTheSystem {
    return Intl.message(
      '(a) To use the System, you may be\nrequired to create an account. You\nagree to provide accurate, current, and\ncomplete information during\nthe registration process and toupdate\nsuchinformationto keep itaccurate\nand complete.',
      name: 'aToUseTheSystem',
      desc: '',
      args: [],
    );
  }

  /// `(b) You are responsible for\nmaintainingthe confidentiality of your\naccount and password andfor restricting\naccess to your account. You accept\nresponsibility for all activities that\noccur under your account.`
  String get bYouHaveResponsiveFor {
    return Intl.message(
      '(b) You are responsible for\nmaintainingthe confidentiality of your\naccount and password andfor restricting\naccess to your account. You accept\nresponsibility for all activities that\noccur under your account.',
      name: 'bYouHaveResponsiveFor',
      desc: '',
      args: [],
    );
  }

  /// `How We Use Your Information`
  String get howWeUseYourInformation {
    return Intl.message(
      'How We Use Your Information',
      name: 'howWeUseYourInformation',
      desc: '',
      args: [],
    );
  }

  /// `We use your personal information to provide you with the best possible experience on our app, including to personalize your content recomme ndations, connect you with experts, and improve our apps functionality. We may also use your information to communicate with you about updates, promotions, or other information related to our app.`
  String get weUseYourPersonalInformation {
    return Intl.message(
      'We use your personal information to provide you with the best possible experience on our app, including to personalize your content recomme ndations, connect you with experts, and improve our apps functionality. We may also use your information to communicate with you about updates, promotions, or other information related to our app.',
      name: 'weUseYourPersonalInformation',
      desc: '',
      args: [],
    );
  }

  /// `How We Protect Your Information`
  String get howWeProtectYourInformation {
    return Intl.message(
      'How We Protect Your Information',
      name: 'howWeProtectYourInformation',
      desc: '',
      args: [],
    );
  }

  /// `We take industry-standard measures to protect your personal information, including encryption and secure storage. We also limit access to your information to authorized personnel only.`
  String get weTakeIndustryStandard {
    return Intl.message(
      'We take industry-standard measures to protect your personal information, including encryption and secure storage. We also limit access to your information to authorized personnel only.',
      name: 'weTakeIndustryStandard',
      desc: '',
      args: [],
    );
  }

  /// `Third-Party Services`
  String get thirdPartyServices {
    return Intl.message(
      'Third-Party Services',
      name: 'thirdPartyServices',
      desc: '',
      args: [],
    );
  }

  /// `We may use third-party services to support our app's functionality, such as analytics providers and payment processors. These third-party services may collect information about you when you use our app. Please note that we are not responsible for the privacy practices of these third-party services.`
  String get weMayUseThirdPartyServicesToSupport {
    return Intl.message(
      'We may use third-party services to support our app\'s functionality, such as analytics providers and payment processors. These third-party services may collect information about you when you use our app. Please note that we are not responsible for the privacy practices of these third-party services.',
      name: 'weMayUseThirdPartyServicesToSupport',
      desc: '',
      args: [],
    );
  }

  /// `Currency`
  String get currency {
    return Intl.message(
      'Currency',
      name: 'currency',
      desc: '',
      args: [],
    );
  }

  /// `POS that contains a great deal of functionality, including sales tracking, inventory management.`
  String get onboardOne {
    return Intl.message(
      'POS that contains a great deal of functionality, including sales tracking, inventory management.',
      name: 'onboardOne',
      desc: '',
      args: [],
    );
  }

  /// `Our POS system should simplify daily operations automatically, making it easy to navigate.`
  String get onboardTwo {
    return Intl.message(
      'Our POS system should simplify daily operations automatically, making it easy to navigate.',
      name: 'onboardTwo',
      desc: '',
      args: [],
    );
  }

  /// `This system helps you improve your operations for your customers.`
  String get onboardThree {
    return Intl.message(
      'This system helps you improve your operations for your customers.',
      name: 'onboardThree',
      desc: '',
      args: [],
    );
  }

  /// `User Role`
  String get userRole {
    return Intl.message(
      'User Role',
      name: 'userRole',
      desc: '',
      args: [],
    );
  }

  /// `No User Role Found`
  String get noUserRoleFound {
    return Intl.message(
      'No User Role Found',
      name: 'noUserRoleFound',
      desc: '',
      args: [],
    );
  }

  /// `Add User Role`
  String get addUserRole {
    return Intl.message(
      'Add User Role',
      name: 'addUserRole',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message(
      'All',
      name: 'all',
      desc: '',
      args: [],
    );
  }

  /// `Profile Edit`
  String get profileEdit {
    return Intl.message(
      'Profile Edit',
      name: 'profileEdit',
      desc: '',
      args: [],
    );
  }

  /// `Enter your password`
  String get enterYourPassword {
    return Intl.message(
      'Enter your password',
      name: 'enterYourPassword',
      desc: '',
      args: [],
    );
  }

  /// `User Title`
  String get UserTitle {
    return Intl.message(
      'User Title',
      name: 'UserTitle',
      desc: '',
      args: [],
    );
  }

  /// `Enter user title`
  String get enterUserTitle {
    return Intl.message(
      'Enter user title',
      name: 'enterUserTitle',
      desc: '',
      args: [],
    );
  }

  /// `Create`
  String get create {
    return Intl.message(
      'Create',
      name: 'create',
      desc: '',
      args: [],
    );
  }

  /// `User Title`
  String get userTitle {
    return Intl.message(
      'User Title',
      name: 'userTitle',
      desc: '',
      args: [],
    );
  }

  /// `Added Successful`
  String get addSuccessful {
    return Intl.message(
      'Added Successful',
      name: 'addSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `You have to RE-LOGIN on your account.`
  String get youHaveToReLogin {
    return Intl.message(
      'You have to RE-LOGIN on your account.',
      name: 'youHaveToReLogin',
      desc: '',
      args: [],
    );
  }

  /// `Ok`
  String get ok {
    return Intl.message(
      'Ok',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `Pay Cash`
  String get payCash {
    return Intl.message(
      'Pay Cash',
      name: 'payCash',
      desc: '',
      args: [],
    );
  }

  /// `Free Lifetime Update`
  String get freeLifeTimeUpdate {
    return Intl.message(
      'Free Lifetime Update',
      name: 'freeLifeTimeUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Android & iOS App Support`
  String get androidIOSAppSupport {
    return Intl.message(
      'Android & iOS App Support',
      name: 'androidIOSAppSupport',
      desc: '',
      args: [],
    );
  }

  /// `Premium Customer Support`
  String get premiumCustomerSupport {
    return Intl.message(
      'Premium Customer Support',
      name: 'premiumCustomerSupport',
      desc: '',
      args: [],
    );
  }

  /// `Custom Invoice Branding`
  String get customInvoiceBranding {
    return Intl.message(
      'Custom Invoice Branding',
      name: 'customInvoiceBranding',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited Usage`
  String get unlimitedUsage {
    return Intl.message(
      'Unlimited Usage',
      name: 'unlimitedUsage',
      desc: '',
      args: [],
    );
  }

  /// `Free Data Backup`
  String get freeDataBackup {
    return Intl.message(
      'Free Data Backup',
      name: 'freeDataBackup',
      desc: '',
      args: [],
    );
  }

  /// `Stay at the forefront of technological advancements without any extra costs. Our AmrDev POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge.`
  String get stayAtTheForFront {
    return Intl.message(
      'Stay at the forefront of technological advancements without any extra costs. Our AmrDev POS Unlimited Upgrade ensures that you always have the latest tools and features at your fingertips, guaranteeing your business remains cutting-edge.',
      name: 'stayAtTheForFront',
      desc: '',
      args: [],
    );
  }

  /// ` We understand the importance of seamless operations. That's why our round-the-clock support is available to assist you, whether it's a quick query or a comprehensive concern. Connect with us anytime, anywhere via call or WhatsApp to experience unrivaled customer service.`
  String get weUnderStand {
    return Intl.message(
      ' We understand the importance of seamless operations. That\'s why our round-the-clock support is available to assist you, whether it\'s a quick query or a comprehensive concern. Connect with us anytime, anywhere via call or WhatsApp to experience unrivaled customer service.',
      name: 'weUnderStand',
      desc: '',
      args: [],
    );
  }

  /// `Unlock the full potential of AmrDev POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you're well-versed in utilizing every facet of the system to optimize your business processes.`
  String get unlockTheFull {
    return Intl.message(
      'Unlock the full potential of AmrDev POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you\'re well-versed in utilizing every facet of the system to optimize your business processes.',
      name: 'unlockTheFull',
      desc: '',
      args: [],
    );
  }

  /// `Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty.`
  String get makeALastingImpression {
    return Intl.message(
      'Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty.',
      name: 'makeALastingImpression',
      desc: '',
      args: [],
    );
  }

  /// `The name says it all. With AmrDev POS Unlimited, there's no cap on your usage. Whether you're processing a handful of transactions or experiencing a rush of customers, you can operate with confidence, knowing you're not constrained by limits`
  String get theNameSysIt {
    return Intl.message(
      'The name says it all. With AmrDev POS Unlimited, there\'s no cap on your usage. Whether you\'re processing a handful of transactions or experiencing a rush of customers, you can operate with confidence, knowing you\'re not constrained by limits',
      name: 'theNameSysIt',
      desc: '',
      args: [],
    );
  }

  /// `Safeguard your business data effortlessly. Our AmrDev POS Unlimited Upgrade includes free data backup, ensuring your valuable information is protected against any unforeseen events. Focus on what truly matters - your business growth.`
  String get safeGuardYourBusinessDate {
    return Intl.message(
      'Safeguard your business data effortlessly. Our AmrDev POS Unlimited Upgrade includes free data backup, ensuring your valuable information is protected against any unforeseen events. Focus on what truly matters - your business growth.',
      name: 'safeGuardYourBusinessDate',
      desc: '',
      args: [],
    );
  }

  /// `Buy`
  String get buy {
    return Intl.message(
      'Buy',
      name: 'buy',
      desc: '',
      args: [],
    );
  }

  /// `Bank Information`
  String get bankInformation {
    return Intl.message(
      'Bank Information',
      name: 'bankInformation',
      desc: '',
      args: [],
    );
  }

  /// `Bank Name`
  String get bankName {
    return Intl.message(
      'Bank Name',
      name: 'bankName',
      desc: '',
      args: [],
    );
  }

  /// `Branch Name`
  String get branchName {
    return Intl.message(
      'Branch Name',
      name: 'branchName',
      desc: '',
      args: [],
    );
  }

  /// `Account Name`
  String get accountName {
    return Intl.message(
      'Account Name',
      name: 'accountName',
      desc: '',
      args: [],
    );
  }

  /// `Account Number`
  String get accountNumber {
    return Intl.message(
      'Account Number',
      name: 'accountNumber',
      desc: '',
      args: [],
    );
  }

  /// `Bank Account Currency`
  String get bankAccountingCurrecny {
    return Intl.message(
      'Bank Account Currency',
      name: 'bankAccountingCurrecny',
      desc: '',
      args: [],
    );
  }

  /// `SWIFT Code`
  String get swiftCode {
    return Intl.message(
      'SWIFT Code',
      name: 'swiftCode',
      desc: '',
      args: [],
    );
  }

  /// `Enter Transaction Id`
  String get enterTransactionId {
    return Intl.message(
      'Enter Transaction Id',
      name: 'enterTransactionId',
      desc: '',
      args: [],
    );
  }

  /// `Upload Document`
  String get uploadDocument {
    return Intl.message(
      'Upload Document',
      name: 'uploadDocument',
      desc: '',
      args: [],
    );
  }

  /// `Upload File`
  String get uploadFile {
    return Intl.message(
      'Upload File',
      name: 'uploadFile',
      desc: '',
      args: [],
    );
  }

  /// `About App`
  String get aboutApp {
    return Intl.message(
      'About App',
      name: 'aboutApp',
      desc: '',
      args: [],
    );
  }

  /// `Terms of use`
  String get termsOfUse {
    return Intl.message(
      'Terms of use',
      name: 'termsOfUse',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `User Role Details`
  String get userRoleDetails {
    return Intl.message(
      'User Role Details',
      name: 'userRoleDetails',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message(
      'Delete',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get update {
    return Intl.message(
      'Update',
      name: 'update',
      desc: '',
      args: [],
    );
  }

  /// `Increase Stock`
  String get increaseStock {
    return Intl.message(
      'Increase Stock',
      name: 'increaseStock',
      desc: '',
      args: [],
    );
  }

  /// `Share`
  String get share {
    return Intl.message(
      'Share',
      name: 'share',
      desc: '',
      args: [],
    );
  }

  /// `Due Collection Reports`
  String get dueCollectionReports {
    return Intl.message(
      'Due Collection Reports',
      name: 'dueCollectionReports',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Reports`
  String get purchaseReportss {
    return Intl.message(
      'Purchase Reports',
      name: 'purchaseReportss',
      desc: '',
      args: [],
    );
  }

  /// `Sale Reports`
  String get saleReportss {
    return Intl.message(
      'Sale Reports',
      name: 'saleReportss',
      desc: '',
      args: [],
    );
  }

  /// `Orders`
  String get order {
    return Intl.message(
      'Orders',
      name: 'order',
      desc: '',
      args: [],
    );
  }

  /// `Revenue`
  String get revenue {
    return Intl.message(
      'Revenue',
      name: 'revenue',
      desc: '',
      args: [],
    );
  }

  /// `Powered By AmrDev POS`
  String get powerdedByMobiPos {
    return Intl.message(
      'Powered By AmrDev 2025',
      name: 'powerdedByMobiPos',
      desc: '',
      args: [],
    );
  }

  /// `Parties`
  String get parties {
    return Intl.message(
      'Parties',
      name: 'parties',
      desc: '',
      args: [],
    );
  }

  /// `Tax`
  String get taxs {
    return Intl.message(
      'Taxs',
      name: 'taxs',
      desc: '',
      args: [],
    );
  }

  /// `Stock List`
  String get stockList {
    return Intl.message(
      'Stock List',
      name: 'stockList',
      desc: '',
      args: [],
    );
  }

  /// `Expense`
  String get expense {
    return Intl.message(
      'Expense',
      name: 'expense',
      desc: '',
      args: [],
    );
  }

  /// `Expense`
  String get cashBox {
    return Intl.message(
      'CashBox',
      name: 'cashBox',
      desc: '',
      args: [],
    );
  }

  /// `You Have Got An Email`
  String get youHaveGotAnEmail {
    return Intl.message(
      'You Have Got An Email',
      name: 'youHaveGotAnEmail',
      desc: '',
      args: [],
    );
  }

  /// `We Have Send An Email with instructions on how to reset password to:`
  String get weHaveSendAnEmailwithInstructions {
    return Intl.message(
      'We Have Send An Email with instructions on how to reset password to:',
      name: 'weHaveSendAnEmailwithInstructions',
      desc: '',
      args: [],
    );
  }

  /// `Check Email`
  String get checkEmail {
    return Intl.message(
      'Check Email',
      name: 'checkEmail',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get otp {
    return Intl.message(
      'Close',
      name: 'otp',
      desc: '',
      args: [],
    );
  }

  /// `Forgot password`
  String get forgotPassword {
    return Intl.message(
      'Forgot password',
      name: 'forgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your email address below to receive password Reset Link.`
  String get pleaseEnterTheEmailAddressBelowToRecive {
    return Intl.message(
      'Please enter your email address below to receive password Reset Link.',
      name: 'pleaseEnterTheEmailAddressBelowToRecive',
      desc: '',
      args: [],
    );
  }

  /// `Email Address`
  String get emailAddress {
    return Intl.message(
      'Email Address',
      name: 'emailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Send Reset Link`
  String get sendResetLink {
    return Intl.message(
      'Send Reset Link',
      name: 'sendResetLink',
      desc: '',
      args: [],
    );
  }

  /// `No user found for that email.`
  String get noUserFoundForThatEmail {
    return Intl.message(
      'No user found for that email.',
      name: 'noUserFoundForThatEmail',
      desc: '',
      args: [],
    );
  }

  /// `Wrong password provided for that user.`
  String get wrongPasswordProvidedforThatUser {
    return Intl.message(
      'Wrong password provided for that user.',
      name: 'wrongPasswordProvidedforThatUser',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message(
      'Email',
      name: 'email',
      desc: '',
      args: [],
    );
  }

  /// `Enter Your Email Address`
  String get enterYourEmailAddress {
    return Intl.message(
      'Enter Your Email Address',
      name: 'enterYourEmailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message(
      'Password',
      name: 'password',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a password`
  String get pleaseEnterAPassword {
    return Intl.message(
      'Please enter a password',
      name: 'pleaseEnterAPassword',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password?`
  String get forgotPasswords {
    return Intl.message(
      'Forgot Password?',
      name: 'forgotPasswords',
      desc: '',
      args: [],
    );
  }

  /// `Log In`
  String get login {
    return Intl.message(
      'Log In',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `Haven't any account?`
  String get havenotAnAccounts {
    return Intl.message(
      'Haven\'t any account?',
      name: 'havenotAnAccounts',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register {
    return Intl.message(
      'Register',
      name: 'register',
      desc: '',
      args: [],
    );
  }

  /// `Manage your business with `
  String get manageYourBussinessWith {
    return Intl.message(
      'Manage your business with ',
      name: 'manageYourBussinessWith',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message(
      'Search',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Start typing to search`
  String get startTypingToSearch {
    return Intl.message(
      'Start typing to search',
      name: 'startTypingToSearch',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number`
  String get enterYourNumber {
    return Intl.message(
      'Enter your phone number',
      name: 'enterYourNumber',
      desc: '',
      args: [],
    );
  }

  /// `Get Otp`
  String get getOtp {
    return Intl.message(
      'Get Otp',
      name: 'getOtp',
      desc: '',
      args: [],
    );
  }

  /// `No Connection`
  String get noConnection {
    return Intl.message(
      'No Connection',
      name: 'noConnection',
      desc: '',
      args: [],
    );
  }

  /// `Please check your internet connectivity`
  String get pleaseCheckYourInternetConnectivity {
    return Intl.message(
      'Please check your internet connectivity',
      name: 'pleaseCheckYourInternetConnectivity',
      desc: '',
      args: [],
    );
  }

  /// `Try Again`
  String get tryAgain {
    return Intl.message(
      'Try Again',
      name: 'tryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Verifying OTP`
  String get verifyOtp {
    return Intl.message(
      'Verifying OTP',
      name: 'verifyOtp',
      desc: '',
      args: [],
    );
  }

  /// `Change?`
  String get change {
    return Intl.message(
      'Change?',
      name: 'change',
      desc: '',
      args: [],
    );
  }

  /// `Resend OTP : `
  String get resendOtp {
    return Intl.message(
      'Resend OTP : ',
      name: 'resendOtp',
      desc: '',
      args: [],
    );
  }

  /// `Resend Code`
  String get resendCode {
    return Intl.message(
      'Resend Code',
      name: 'resendCode',
      desc: '',
      args: [],
    );
  }

  /// `Verify Phone Number`
  String get verifyPhoneNumber {
    return Intl.message(
      'Verify Phone Number',
      name: 'verifyPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Setup Your Profile`
  String get setUpYourProfile {
    return Intl.message(
      'Setup Your Profile',
      name: 'setUpYourProfile',
      desc: '',
      args: [],
    );
  }

  /// `Update your profile to connect your doctor with better impression`
  String get updateYourProfileToConnect {
    return Intl.message(
      'Update your profile to connect your doctor with better impression',
      name: 'updateYourProfileToConnect',
      desc: '',
      args: [],
    );
  }

  /// `Gallery`
  String get gallary {
    return Intl.message(
      'Gallery',
      name: 'gallary',
      desc: '',
      args: [],
    );
  }

  /// `Camera`
  String get camera {
    return Intl.message(
      'Camera',
      name: 'camera',
      desc: '',
      args: [],
    );
  }

  /// `Business Category`
  String get businessCategory {
    return Intl.message(
      'Business Category',
      name: 'businessCategory',
      desc: '',
      args: [],
    );
  }

  /// `Company & Shop Name`
  String get companyAndShopName {
    return Intl.message(
      'Company & Shop Name',
      name: 'companyAndShopName',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get phoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter Phone Number`
  String get enterPhoneNumber {
    return Intl.message(
      'Enter Phone Number',
      name: 'enterPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Company Address`
  String get companyAddress {
    return Intl.message(
      'Company Address',
      name: 'companyAddress',
      desc: '',
      args: [],
    );
  }

  /// `Enter Full Address`
  String get enterFullAddress {
    return Intl.message(
      'Enter Full Address',
      name: 'enterFullAddress',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get language {
    return Intl.message(
      'Language',
      name: 'language',
      desc: '',
      args: [],
    );
  }

  /// `Opening Balance `
  String get openingBalance {
    return Intl.message(
      'Opening Balance ',
      name: 'openingBalance',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continu {
    return Intl.message(
      'Continue',
      name: 'continu',
      desc: '',
      args: [],
    );
  }

  /// `Create a Free Account`
  String get createAFreeAccounts {
    return Intl.message(
      'Create a Free Account',
      name: 'createAFreeAccounts',
      desc: '',
      args: [],
    );
  }

  /// `LogIn`
  String get logIn {
    return Intl.message(
      'LogIn',
      name: 'logIn',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations`
  String get congratulations {
    return Intl.message(
      'Congratulations',
      name: 'congratulations',
      desc: '',
      args: [],
    );
  }

  /// `You are successfully login into your account. Stay with AmrDev POS .`
  String get youHaveSuccefulyLogin {
    return Intl.message(
      'You are successfully login into your account. Stay with AmrDev POS .',
      name: 'youHaveSuccefulyLogin',
      desc: '',
      args: [],
    );
  }

  /// `Add Contact`
  String get addContact {
    return Intl.message(
      'Add Contact',
      name: 'addContact',
      desc: '',
      args: [],
    );
  }

  /// `Enter Your Phone Number`
  String get enterYourPhoneNumber {
    return Intl.message(
      'Enter Your Phone Number',
      name: 'enterYourPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter Your Name.`
  String get name {
    return Intl.message(
      'Enter Your Name.',
      name: 'name',
      desc: '',
      args: [],
    );
  }

  /// `Enter Amount.`
  String get enterAmount {
    return Intl.message(
      'Enter Amount.',
      name: 'enterAmount',
      desc: '',
      args: [],
    );
  }

  /// `Retailer`
  String get retailer {
    return Intl.message(
      'Retailer',
      name: 'retailer',
      desc: '',
      args: [],
    );
  }

  /// `Dealer`
  String get dealer {
    return Intl.message(
      'Dealer',
      name: 'dealer',
      desc: '',
      args: [],
    );
  }

  /// `Wholesaler`
  String get wholSeller {
    return Intl.message(
      'Wholesaler',
      name: 'wholSeller',
      desc: '',
      args: [],
    );
  }

  /// `Supplier`
  String get supplier {
    return Intl.message(
      'Supplier',
      name: 'supplier',
      desc: '',
      args: [],
    );
  }

  /// `More Info`
  String get moreInfo {
    return Intl.message(
      'More Info',
      name: 'moreInfo',
      desc: '',
      args: [],
    );
  }

  /// `Address`
  String get address {
    return Intl.message(
      'Address',
      name: 'address',
      desc: '',
      args: [],
    );
  }

  /// `Enter Address`
  String get enterAddress {
    return Intl.message(
      'Enter Address',
      name: 'enterAddress',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message(
      'Save',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `Customer Details`
  String get customerDetails {
    return Intl.message(
      'Customer Details',
      name: 'customerDetails',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this user?`
  String get areYourSureDeleteThisUser {
    return Intl.message(
      'Are you sure to delete this user?',
      name: 'areYourSureDeleteThisUser',
      desc: '',
      args: [],
    );
  }

  /// `The user will be deleted and all the data will be deleted from your account.Are you sure to delete this?`
  String get theUserWillBe {
    return Intl.message(
      'The user will be deleted and all the data will be deleted from your account.Are you sure to delete this?',
      name: 'theUserWillBe',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cacel {
    return Intl.message(
      'Cancel',
      name: 'cacel',
      desc: '',
      args: [],
    );
  }

  /// `Yes, Delete Forever`
  String get yesDeleteForever {
    return Intl.message(
      'Yes, Delete Forever',
      name: 'yesDeleteForever',
      desc: '',
      args: [],
    );
  }

  /// `Call`
  String get call {
    return Intl.message(
      'Call',
      name: 'call',
      desc: '',
      args: [],
    );
  }

  /// `Message`
  String get message {
    return Intl.message(
      'Message',
      name: 'message',
      desc: '',
      args: [],
    );
  }

  /// `Recent Transactions`
  String get recentTransactions {
    return Intl.message(
      'Recent Transactions',
      name: 'recentTransactions',
      desc: '',
      args: [],
    );
  }

  /// `Click to connect`
  String get clickToConnect {
    return Intl.message(
      'Click to connect',
      name: 'clickToConnect',
      desc: '',
      args: [],
    );
  }

  /// `Please connect your bluetooth Printer`
  String get pleaseConnectYourBluttothPrinter {
    return Intl.message(
      'Please connect your bluetooth Printer',
      name: 'pleaseConnectYourBluttothPrinter',
      desc: '',
      args: [],
    );
  }

  /// `View All`
  String get viewAll {
    return Intl.message(
      'View All',
      name: 'viewAll',
      desc: '',
      args: [],
    );
  }

  /// `Parties List`
  String get partiesList {
    return Intl.message(
      'Parties List',
      name: 'partiesList',
      desc: '',
      args: [],
    );
  }

  /// `Party Name`
  String get partyName {
    return Intl.message(
      'Party Name',
      name: 'partyName',
      desc: '',
      args: [],
    );
  }

  /// `Enter Party Name`
  String get enterPartyName {
    return Intl.message(
      'Enter Party Name',
      name: 'enterPartyName',
      desc: '',
      args: [],
    );
  }

  /// `Due`
  String get due {
    return Intl.message(
      'Due',
      name: 'due',
      desc: '',
      args: [],
    );
  }

  /// `Update Contact`
  String get updateContact {
    return Intl.message(
      'Update Contact',
      name: 'updateContact',
      desc: '',
      args: [],
    );
  }

  /// `Supplier`
  String get upplier {
    return Intl.message(
      'Supplier',
      name: 'upplier',
      desc: '',
      args: [],
    );
  }

  /// `Add New Address`
  String get addNewAddress {
    return Intl.message(
      'Add New Address',
      name: 'addNewAddress',
      desc: '',
      args: [],
    );
  }

  /// `First Name`
  String get firstName {
    return Intl.message(
      'First Name',
      name: 'firstName',
      desc: '',
      args: [],
    );
  }

  /// `Enter Your Name`
  String get enterYourName {
    return Intl.message(
      'Enter Your Name',
      name: 'enterYourName',
      desc: '',
      args: [],
    );
  }

  /// `Last Name`
  String get lastName {
    return Intl.message(
      'Last Name',
      name: 'lastName',
      desc: '',
      args: [],
    );
  }

  /// `Country`
  String get country {
    return Intl.message(
      'Country',
      name: 'country',
      desc: '',
      args: [],
    );
  }

  /// `Egypt`
  String get egypt {
    return Intl.message(
      'Egypt',
      name: 'egypt',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get apply {
    return Intl.message(
      'Apply',
      name: 'apply',
      desc: '',
      args: [],
    );
  }

  /// `Delivery Address`
  String get deliveryAddress {
    return Intl.message(
      'Delivery Address',
      name: 'deliveryAddress',
      desc: '',
      args: [],
    );
  }

  /// `No data available`
  String get noDataAvailable {
    return Intl.message(
      'No data available',
      name: 'noDataAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Add Delivery`
  String get addDelivery {
    return Intl.message(
      'Add Delivery',
      name: 'addDelivery',
      desc: '',
      args: [],
    );
  }

  /// `Due List`
  String get dueList {
    return Intl.message(
      'Due List',
      name: 'dueList',
      desc: '',
      args: [],
    );
  }

  /// `Due Collection`
  String get dueCollection {
    return Intl.message(
      'Due Collection',
      name: 'dueCollection',
      desc: '',
      args: [],
    );
  }

  /// `Due Amount: `
  String get dueAmount {
    return Intl.message(
      'Due Amount: ',
      name: 'dueAmount',
      desc: '',
      args: [],
    );
  }

  /// `Total Amount`
  String get totalAmount {
    return Intl.message(
      'Total Amount',
      name: 'totalAmount',
      desc: '',
      args: [],
    );
  }

  /// `Paid Amount`
  String get paidAmount {
    return Intl.message(
      'Paid Amount',
      name: 'paidAmount',
      desc: '',
      args: [],
    );
  }

  /// `Payment Type`
  String get paymentType {
    return Intl.message(
      'Payment Type',
      name: 'paymentType',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get describtion {
    return Intl.message(
      'Description',
      name: 'describtion',
      desc: '',
      args: [],
    );
  }

  /// `Add note`
  String get addDescription {
    return Intl.message(
      'Add note',
      name: 'addDescription',
      desc: '',
      args: [],
    );
  }

  /// `Image`
  String get image {
    return Intl.message(
      'Image',
      name: 'image',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Password`
  String get confirmPassword {
    return Intl.message(
      'Confirm Password',
      name: 'confirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please Enter A Confirm Password`
  String get pleaseEnterAConfirmPassword {
    return Intl.message(
      'Please Enter A Confirm Password',
      name: 'pleaseEnterAConfirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Already Have An Accounts`
  String get alreadyHaveAnAccounts {
    return Intl.message(
      'Already Have An Accounts',
      name: 'alreadyHaveAnAccounts',
      desc: '',
      args: [],
    );
  }

  /// `Add Customer`
  String get addCustomer {
    return Intl.message(
      'Add Customer',
      name: 'addCustomer',
      desc: '',
      args: [],
    );
  }

  /// `Date`
  String get date {
    return Intl.message(
      'Date',
      name: 'date',
      desc: '',
      args: [],
    );
  }

  /// `Customer Name`
  String get customerName {
    return Intl.message(
      'Customer Name',
      name: 'customerName',
      desc: '',
      args: [],
    );
  }

  /// `Add Note`
  String get addNote {
    return Intl.message(
      'Add Note',
      name: 'addNote',
      desc: '',
      args: [],
    );
  }

  /// `Add Expense`
  String get addExpense {
    return Intl.message(
      'Add Expense',
      name: 'addExpense',
      desc: '',
      args: [],
    );
  }

  /// `Expense Date`
  String get expenseDate {
    return Intl.message(
      'Expense Date',
      name: 'expenseDate',
      desc: '',
      args: [],
    );
  }

  /// `Enter Expense Date`
  String get enterExpenseDate {
    return Intl.message(
      'Enter Expense Date',
      name: 'enterExpenseDate',
      desc: '',
      args: [],
    );
  }

  /// `Expense For`
  String get expenseFor {
    return Intl.message(
      'Expense For',
      name: 'expenseFor',
      desc: '',
      args: [],
    );
  }

  /// `Enter Name`
  String get enterName {
    return Intl.message(
      'Enter Name',
      name: 'enterName',
      desc: '',
      args: [],
    );
  }

  /// `Amount`
  String get amount {
    return Intl.message(
      'Amount',
      name: 'amount',
      desc: '',
      args: [],
    );
  }

  /// `Reference Number`
  String get referenceNumber {
    return Intl.message(
      'Reference Number',
      name: 'referenceNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter Reference Number`
  String get enterReferenceNumber {
    return Intl.message(
      'Enter Reference Number',
      name: 'enterReferenceNumber',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get note {
    return Intl.message(
      'Note',
      name: 'note',
      desc: '',
      args: [],
    );
  }

  /// `Enter Note`
  String get enterNote {
    return Intl.message(
      'Enter Note',
      name: 'enterNote',
      desc: '',
      args: [],
    );
  }

  /// `Add Expense Category`
  String get addExpenseCategory {
    return Intl.message(
      'Add Expense Category',
      name: 'addExpenseCategory',
      desc: '',
      args: [],
    );
  }

  /// `Fashion`
  String get fashion {
    return Intl.message(
      'Fashion',
      name: 'fashion',
      desc: '',
      args: [],
    );
  }

  /// `Category Name`
  String get cateogryName {
    return Intl.message(
      'Category Name',
      name: 'cateogryName',
      desc: '',
      args: [],
    );
  }

  /// `Expense Category`
  String get expenseCategory {
    return Intl.message(
      'Expense Category',
      name: 'expenseCategory',
      desc: '',
      args: [],
    );
  }

  /// `Select`
  String get select {
    return Intl.message(
      'Select',
      name: 'select',
      desc: '',
      args: [],
    );
  }

  /// `Expense Report`
  String get expenseReport {
    return Intl.message(
      'Expense Report',
      name: 'expenseReport',
      desc: '',
      args: [],
    );
  }

  /// `From Date`
  String get formDate {
    return Intl.message(
      'From Date',
      name: 'formDate',
      desc: '',
      args: [],
    );
  }

  /// `To Date`
  String get toDate {
    return Intl.message(
      'To Date',
      name: 'toDate',
      desc: '',
      args: [],
    );
  }

  /// `Total Expense`
  String get totalExpense {
    return Intl.message(
      'Total Expense',
      name: 'totalExpense',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get home {
    return Intl.message(
      'Home',
      name: 'home',
      desc: '',
      args: [],
    );
  }

  /// `Sales`
  String get sales {
    return Intl.message(
      'Sales',
      name: 'sales',
      desc: '',
      args: [],
    );
  }

  /// `Reports`
  String get reports {
    return Intl.message(
      'Reports',
      name: 'reports',
      desc: '',
      args: [],
    );
  }

  /// `Setting`
  String get setting {
    return Intl.message(
      'Setting',
      name: 'setting',
      desc: '',
      args: [],
    );
  }

  /// `What's New`
  String get whatsNew {
    return Intl.message(
      'What\'s New',
      name: 'whatsNew',
      desc: '',
      args: [],
    );
  }

  /// `Maan`
  String get maan {
    return Intl.message(
      'Maan',
      name: 'maan',
      desc: '',
      args: [],
    );
  }

  /// `Package`
  String get pacakge {
    return Intl.message(
      'Package',
      name: 'pacakge',
      desc: '',
      args: [],
    );
  }

  /// `Bill To`
  String get billTo {
    return Intl.message(
      'Bill To',
      name: 'billTo',
      desc: '',
      args: [],
    );
  }

  /// `Total Due`
  String get totalDue {
    return Intl.message(
      'Total Due',
      name: 'totalDue',
      desc: '',
      args: [],
    );
  }

  /// `Payment Amount`
  String get paymentAmount {
    return Intl.message(
      'Payment Amount',
      name: 'paymentAmount',
      desc: '',
      args: [],
    );
  }

  /// `Remaining Due`
  String get remainingDue {
    return Intl.message(
      'Remaining Due',
      name: 'remainingDue',
      desc: '',
      args: [],
    );
  }

  /// `Thank You For Your DUe Payment`
  String get thankYOuForYourDuePayment {
    return Intl.message(
      'Thank You For Your DUe Payment',
      name: 'thankYOuForYourDuePayment',
      desc: '',
      args: [],
    );
  }

  /// `Print`
  String get print {
    return Intl.message(
      'Print',
      name: 'print',
      desc: '',
      args: [],
    );
  }

  /// `Product`
  String get product {
    return Intl.message(
      'Product',
      name: 'product',
      desc: '',
      args: [],
    );
  }

  /// `Quantity`
  String get quantity {
    return Intl.message(
      'Quantity',
      name: 'quantity',
      desc: '',
      args: [],
    );
  }

  /// `Unit Price`
  String get unitPirce {
    return Intl.message(
      'Unit Price',
      name: 'unitPirce',
      desc: '',
      args: [],
    );
  }

  /// `Total Price`
  String get totalPrice {
    return Intl.message(
      'Total Price',
      name: 'totalPrice',
      desc: '',
      args: [],
    );
  }

  /// `Sub Total`
  String get subTotal {
    return Intl.message(
      'Sub Total',
      name: 'subTotal',
      desc: '',
      args: [],
    );
  }

  /// `Total Vat`
  String get totalVat {
    return Intl.message(
      'Total Vat',
      name: 'totalVat',
      desc: '',
      args: [],
    );
  }

  /// `Discount`
  String get discount {
    return Intl.message(
      'Discount',
      name: 'discount',
      desc: '',
      args: [],
    );
  }

  /// `Delivery Charge`
  String get deliveryCharge {
    return Intl.message(
      'Delivery Charge',
      name: 'deliveryCharge',
      desc: '',
      args: [],
    );
  }

  /// `Total Payable`
  String get totalPayable {
    return Intl.message(
      'Total Payable',
      name: 'totalPayable',
      desc: '',
      args: [],
    );
  }

  /// `Paid`
  String get paid {
    return Intl.message(
      'Paid',
      name: 'paid',
      desc: '',
      args: [],
    );
  }

  /// `Thank You for your purchase`
  String get thankYouForYourPurchase {
    return Intl.message(
      'Thank You for your purchase',
      name: 'thankYouForYourPurchase',
      desc: '',
      args: [],
    );
  }

  /// `Total Sale`
  String get totalSale {
    return Intl.message(
      'Total Sale',
      name: 'totalSale',
      desc: '',
      args: [],
    );
  }

  /// `Ledger`
  String get ledger {
    return Intl.message(
      'Ledger',
      name: 'ledger',
      desc: '',
      args: [],
    );
  }

  /// `Loss/Profit`
  String get lossOrProfit {
    return Intl.message(
      'Loss/Profit',
      name: 'lossOrProfit',
      desc: '',
      args: [],
    );
  }

  /// `Profit`
  String get profit {
    return Intl.message(
      'Profit',
      name: 'profit',
      desc: '',
      args: [],
    );
  }

  /// `Loss`
  String get loss {
    return Intl.message(
      'Loss',
      name: 'loss',
      desc: '',
      args: [],
    );
  }

  /// `Loss/Profit Details`
  String get lossOrProfitDetails {
    return Intl.message(
      'Loss/Profit Details',
      name: 'lossOrProfitDetails',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get total {
    return Intl.message(
      'Total',
      name: 'total',
      desc: '',
      args: [],
    );
  }

  /// `Edit Social Media`
  String get editSocailMedia {
    return Intl.message(
      'Edit Social Media',
      name: 'editSocailMedia',
      desc: '',
      args: [],
    );
  }

  /// `Facebook`
  String get facebok {
    return Intl.message(
      'Facebook',
      name: 'facebok',
      desc: '',
      args: [],
    );
  }

  /// `Twitter`
  String get twitter {
    return Intl.message(
      'Twitter',
      name: 'twitter',
      desc: '',
      args: [],
    );
  }

  /// `Instagram`
  String get instragram {
    return Intl.message(
      'Instagram',
      name: 'instragram',
      desc: '',
      args: [],
    );
  }

  /// `LinkedIn`
  String get linkedIn {
    return Intl.message(
      'LinkedIn',
      name: 'linkedIn',
      desc: '',
      args: [],
    );
  }

  /// `Link`
  String get link {
    return Intl.message(
      'Link',
      name: 'link',
      desc: '',
      args: [],
    );
  }

  /// `Social Marketing`
  String get socailMarketing {
    return Intl.message(
      'Social Marketing',
      name: 'socailMarketing',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message(
      'Edit',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Notification`
  String get notification {
    return Intl.message(
      'Notification',
      name: 'notification',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Alarm`
  String get purchaseAlarm {
    return Intl.message(
      'Purchase Alarm',
      name: 'purchaseAlarm',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Confirmed`
  String get purchaseConfirmed {
    return Intl.message(
      'Purchase Confirmed',
      name: 'purchaseConfirmed',
      desc: '',
      args: [],
    );
  }

  /// `Payment Complete`
  String get paymentComplete {
    return Intl.message(
      'Payment Complete',
      name: 'paymentComplete',
      desc: '',
      args: [],
    );
  }

  /// `Return`
  String get retur {
    return Intl.message(
      'Return',
      name: 'retur',
      desc: '',
      args: [],
    );
  }

  /// `Send Email`
  String get sendEmail {
    return Intl.message(
      'Send Email',
      name: 'sendEmail',
      desc: '',
      args: [],
    );
  }

  /// `Send Sms`
  String get sendSms {
    return Intl.message(
      'Send Sms',
      name: 'sendSms',
      desc: '',
      args: [],
    );
  }

  /// `Received The pin`
  String get recivedThePin {
    return Intl.message(
      'Received The pin',
      name: 'recivedThePin',
      desc: '',
      args: [],
    );
  }

  /// `Start New Sale`
  String get startNewSale {
    return Intl.message(
      'Start New Sale',
      name: 'startNewSale',
      desc: '',
      args: [],
    );
  }

  /// `Payment`
  String get payment {
    return Intl.message(
      'Payment',
      name: 'payment',
      desc: '',
      args: [],
    );
  }

  /// `Master card`
  String get masterCard {
    return Intl.message(
      'Master card',
      name: 'masterCard',
      desc: '',
      args: [],
    );
  }

  /// `Instrument`
  String get inistrument {
    return Intl.message(
      'Instrument',
      name: 'inistrument',
      desc: '',
      args: [],
    );
  }

  /// `Cash`
  String get cash {
    return Intl.message(
      'Cash',
      name: 'cash',
      desc: '',
      args: [],
    );
  }

  /// `Add Brand`
  String get addBrand {
    return Intl.message(
      'Add Brand',
      name: 'addBrand',
      desc: '',
      args: [],
    );
  }

  /// `Brand Name`
  String get brandName {
    return Intl.message(
      'Brand Name',
      name: 'brandName',
      desc: '',
      args: [],
    );
  }

  /// `Enter Brand Name`
  String get enterBrandName {
    return Intl.message(
      'Enter Brand Name',
      name: 'enterBrandName',
      desc: '',
      args: [],
    );
  }

  /// `Add Category`
  String get addCategory {
    return Intl.message(
      'Add Category',
      name: 'addCategory',
      desc: '',
      args: [],
    );
  }

  /// `Enter Category Name`
  String get enterCategoryName {
    return Intl.message(
      'Enter Category Name',
      name: 'enterCategoryName',
      desc: '',
      args: [],
    );
  }

  /// `Select Variation: `
  String get selectvariations {
    return Intl.message(
      'Select Variation: ',
      name: 'selectvariations',
      desc: '',
      args: [],
    );
  }

  /// `Size`
  String get size {
    return Intl.message(
      'Size',
      name: 'size',
      desc: '',
      args: [],
    );
  }

  /// `Color`
  String get color {
    return Intl.message(
      'Color',
      name: 'color',
      desc: '',
      args: [],
    );
  }

  /// `Weight`
  String get weight {
    return Intl.message(
      'Weight',
      name: 'weight',
      desc: '',
      args: [],
    );
  }

  /// `Capacity`
  String get capacity {
    return Intl.message(
      'Capacity',
      name: 'capacity',
      desc: '',
      args: [],
    );
  }

  /// `Type`
  String get type {
    return Intl.message(
      'Type',
      name: 'type',
      desc: '',
      args: [],
    );
  }

  /// `Add Product`
  String get addNewProduct {
    return Intl.message(
      'Add Product',
      name: 'addNewProduct',
      desc: '',
      args: [],
    );
  }

  /// `Product Name`
  String get productName {
    return Intl.message(
      'Product Name',
      name: 'productName',
      desc: '',
      args: [],
    );
  }

  /// `Enter Product Name`
  String get enterProductName {
    return Intl.message(
      'Enter Product Name',
      name: 'enterProductName',
      desc: '',
      args: [],
    );
  }

  /// `Category`
  String get category {
    return Intl.message(
      'Category',
      name: 'category',
      desc: '',
      args: [],
    );
  }

  /// `Enter Size`
  String get enterSize {
    return Intl.message(
      'Enter Size',
      name: 'enterSize',
      desc: '',
      args: [],
    );
  }

  /// `Enter Color`
  String get enterColor {
    return Intl.message(
      'Enter Color',
      name: 'enterColor',
      desc: '',
      args: [],
    );
  }

  /// `Enter Weight`
  String get enterWeight {
    return Intl.message(
      'Enter Weight',
      name: 'enterWeight',
      desc: '',
      args: [],
    );
  }

  /// `Enter Capacity`
  String get enterCapacity {
    return Intl.message(
      'Enter Capacity',
      name: 'enterCapacity',
      desc: '',
      args: [],
    );
  }

  /// `Enter Type`
  String get enterType {
    return Intl.message(
      'Enter Type',
      name: 'enterType',
      desc: '',
      args: [],
    );
  }

  /// `Brand`
  String get brand {
    return Intl.message(
      'Brand',
      name: 'brand',
      desc: '',
      args: [],
    );
  }

  /// `Product Code`
  String get productCode {
    return Intl.message(
      'Product Code',
      name: 'productCode',
      desc: '',
      args: [],
    );
  }

  /// `Enter Product Code Or Scan`
  String get enterProductCodeOrScan {
    return Intl.message(
      'Enter Product Code Or Scan',
      name: 'enterProductCodeOrScan',
      desc: '',
      args: [],
    );
  }

  /// `Stocks`
  String get stocks {
    return Intl.message(
      'Stocks',
      name: 'stocks',
      desc: '',
      args: [],
    );
  }

  /// `Enter Stocks.`
  String get enterStocks {
    return Intl.message(
      'Enter Stocks.',
      name: 'enterStocks',
      desc: '',
      args: [],
    );
  }

  /// `Units`
  String get units {
    return Intl.message(
      'Units',
      name: 'units',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Price`
  String get purchasePrice {
    return Intl.message(
      'Purchase Price',
      name: 'purchasePrice',
      desc: '',
      args: [],
    );
  }

  /// `Enter Purchase Price.`
  String get enterPurchasePrice {
    return Intl.message(
      'Enter Purchase Price.',
      name: 'enterPurchasePrice',
      desc: '',
      args: [],
    );
  }

  /// `MRP`
  String get MRP {
    return Intl.message(
      'MRP',
      name: 'MRP',
      desc: '',
      args: [],
    );
  }

  /// `Enter MRP/Retailer Price`
  String get enterMrpOrRetailerPirce {
    return Intl.message(
      'Enter MRP/Retailer Price',
      name: 'enterMrpOrRetailerPirce',
      desc: '',
      args: [],
    );
  }

  /// `WholeSale Price`
  String get wholeSalePrice {
    return Intl.message(
      'WholeSale Price',
      name: 'wholeSalePrice',
      desc: '',
      args: [],
    );
  }

  /// `Enter Wholesale Price`
  String get enterWholeSalePrice {
    return Intl.message(
      'Enter Wholesale Price',
      name: 'enterWholeSalePrice',
      desc: '',
      args: [],
    );
  }

  /// `Dealer Price`
  String get dealerPrice {
    return Intl.message(
      'Dealer Price',
      name: 'dealerPrice',
      desc: '',
      args: [],
    );
  }

  /// `Enter Dealer Price`
  String get enterDealerPrice {
    return Intl.message(
      'Enter Dealer Price',
      name: 'enterDealerPrice',
      desc: '',
      args: [],
    );
  }

  /// `Enter Discount.`
  String get enterDiscount {
    return Intl.message(
      'Enter Discount.',
      name: 'enterDiscount',
      desc: '',
      args: [],
    );
  }

  /// `Manufacturer`
  String get menufeturer {
    return Intl.message(
      'Manufacturer',
      name: 'menufeturer',
      desc: '',
      args: [],
    );
  }

  /// `Enter Manufacturer`
  String get enterManufacturer {
    return Intl.message(
      'Enter Manufacturer',
      name: 'enterManufacturer',
      desc: '',
      args: [],
    );
  }

  /// `Save and Publish`
  String get saveAndPublish {
    return Intl.message(
      'Save and Publish',
      name: 'saveAndPublish',
      desc: '',
      args: [],
    );
  }

  /// `Add Unit`
  String get addUnit {
    return Intl.message(
      'Add Unit',
      name: 'addUnit',
      desc: '',
      args: [],
    );
  }

  /// `Kg`
  String get kg {
    return Intl.message(
      'Kg',
      name: 'kg',
      desc: '',
      args: [],
    );
  }

  /// `Unit Name`
  String get unitName {
    return Intl.message(
      'Unit Name',
      name: 'unitName',
      desc: '',
      args: [],
    );
  }

  /// `Categories`
  String get categories {
    return Intl.message(
      'Categories',
      name: 'categories',
      desc: '',
      args: [],
    );
  }

  /// `Product List`
  String get productList {
    return Intl.message(
      'Product List',
      name: 'productList',
      desc: '',
      args: [],
    );
  }

  /// `Update Product`
  String get updateProduct {
    return Intl.message(
      'Update Product',
      name: 'updateProduct',
      desc: '',
      args: [],
    );
  }

  /// `Clarence`
  String get clarence {
    return Intl.message(
      'Clarence',
      name: 'clarence',
      desc: '',
      args: [],
    );
  }

  /// `Daily Transaction`
  String get dailyTransaciton {
    return Intl.message(
      'Daily Transaction',
      name: 'dailyTransaciton',
      desc: '',
      args: [],
    );
  }

  /// `promo`
  String get promo {
    return Intl.message(
      'promo',
      name: 'promo',
      desc: '',
      args: [],
    );
  }

  /// `Update Your Profile`
  String get updateYourProfile {
    return Intl.message(
      'Update Your Profile',
      name: 'updateYourProfile',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get send {
    return Intl.message(
      'Send',
      name: 'send',
      desc: '',
      args: [],
    );
  }

  /// `Update your profile to connect your customer with better impression`
  String get updateYourProfiletoConnectTOCusomter {
    return Intl.message(
      'Update your profile to connect your customer with better impression',
      name: 'updateYourProfiletoConnectTOCusomter',
      desc: '',
      args: [],
    );
  }

  /// `Update Now`
  String get updateNow {
    return Intl.message(
      'Update Now',
      name: 'updateNow',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message(
      'Profile',
      name: 'profile',
      desc: '',
      args: [],
    );
  }

  /// `Change Password`
  String get changePassword {
    return Intl.message(
      'Change Password',
      name: 'changePassword',
      desc: '',
      args: [],
    );
  }

  /// `Add Purchase`
  String get addPurchase {
    return Intl.message(
      'Add Purchase',
      name: 'addPurchase',
      desc: '',
      args: [],
    );
  }

  /// `Inv No.`
  String get invNo {
    return Intl.message(
      'Inv No.',
      name: 'invNo',
      desc: '',
      args: [],
    );
  }

  /// `Supplier Name`
  String get supplierName {
    return Intl.message(
      'Supplier Name',
      name: 'supplierName',
      desc: '',
      args: [],
    );
  }

  /// `Item Added`
  String get itemAdded {
    return Intl.message(
      'Item Added',
      name: 'itemAdded',
      desc: '',
      args: [],
    );
  }

  /// `Add Items`
  String get addItems {
    return Intl.message(
      'Add Items',
      name: 'addItems',
      desc: '',
      args: [],
    );
  }

  /// `Return Amount`
  String get returnAMount {
    return Intl.message(
      'Return Amount',
      name: 'returnAMount',
      desc: '',
      args: [],
    );
  }

  /// `Chose a Supplier`
  String get choseASupplier {
    return Intl.message(
      'Chose a Supplier',
      name: 'choseASupplier',
      desc: '',
      args: [],
    );
  }

  /// `Chose a Customer`
  String get choseACustomer {
    return Intl.message(
      'Chose a Customer',
      name: 'choseACustomer',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Details`
  String get purchaseDetails {
    return Intl.message(
      'Purchase Details',
      name: 'purchaseDetails',
      desc: '',
      args: [],
    );
  }

  /// `Purchase List`
  String get purchaseList {
    return Intl.message(
      'Purchase List',
      name: 'purchaseList',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message(
      'Start Date',
      name: 'startDate',
      desc: '',
      args: [],
    );
  }

  /// `Pick Start Date`
  String get pickStartDate {
    return Intl.message(
      'Pick Start Date',
      name: 'pickStartDate',
      desc: '',
      args: [],
    );
  }

  /// `End Date`
  String get endDate {
    return Intl.message(
      'End Date',
      name: 'endDate',
      desc: '',
      args: [],
    );
  }

  /// `Pick End Date`
  String get pickEndDate {
    return Intl.message(
      'Pick End Date',
      name: 'pickEndDate',
      desc: '',
      args: [],
    );
  }

  /// `Total: `
  String get totals {
    return Intl.message(
      'Total: ',
      name: 'totals',
      desc: '',
      args: [],
    );
  }

  /// `Sale Price`
  String get salePrice {
    return Intl.message(
      'Sale Price',
      name: 'salePrice',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Price`
  String get purchaseReports {
    return Intl.message(
      'Purchase Price',
      name: 'purchaseReports',
      desc: '',
      args: [],
    );
  }

  /// `Qty`
  String get qty {
    return Intl.message(
      'Qty',
      name: 'qty',
      desc: '',
      args: [],
    );
  }

  /// `price`
  String get price {
    return Intl.message(
      'price',
      name: 'price',
      desc: '',
      args: [],
    );
  }

  /// `Purchase`
  String get purchase {
    return Intl.message(
      'Purchase',
      name: 'purchase',
      desc: '',
      args: [],
    );
  }

  /// `Sale Details`
  String get saleDetails {
    return Intl.message(
      'Sale Details',
      name: 'saleDetails',
      desc: '',
      args: [],
    );
  }

  /// `Edit Purchase Invoice`
  String get editPurchaseInvoice {
    return Intl.message(
      'Edit Purchase Invoice',
      name: 'editPurchaseInvoice',
      desc: '',
      args: [],
    );
  }

  /// `Invoice Number`
  String get invoiceNumber {
    return Intl.message(
      'Invoice Number',
      name: 'invoiceNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter Invoice Number`
  String get enterInvoiceNumber {
    return Intl.message(
      'Enter Invoice Number',
      name: 'enterInvoiceNumber',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Report`
  String get purchaseRepoet {
    return Intl.message(
      'Purchase Report',
      name: 'purchaseRepoet',
      desc: '',
      args: [],
    );
  }

  /// `Sale Report`
  String get saleReports {
    return Intl.message(
      'Sale Report',
      name: 'saleReports',
      desc: '',
      args: [],
    );
  }

  /// `Due Report`
  String get dueReports {
    return Intl.message(
      'Due Report',
      name: 'dueReports',
      desc: '',
      args: [],
    );
  }

  /// `Promo Code`
  String get promoCode {
    return Intl.message(
      'Promo Code',
      name: 'promoCode',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message(
      'Submit',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `See all promo codes`
  String get seeAllPromoCode {
    return Intl.message(
      'See all promo codes',
      name: 'seeAllPromoCode',
      desc: '',
      args: [],
    );
  }

  /// `Add Sales`
  String get addSales {
    return Intl.message(
      'Add Sales',
      name: 'addSales',
      desc: '',
      args: [],
    );
  }

  /// `Send sms?`
  String get sendSmsw {
    return Intl.message(
      'Send sms?',
      name: 'sendSmsw',
      desc: '',
      args: [],
    );
  }

  /// `Walk-in customer`
  String get walkInCustomer {
    return Intl.message(
      'Walk-in customer',
      name: 'walkInCustomer',
      desc: '',
      args: [],
    );
  }

  /// `Guest`
  String get guest {
    return Intl.message(
      'Guest',
      name: 'guest',
      desc: '',
      args: [],
    );
  }

  /// `Sales List`
  String get salesList {
    return Intl.message(
      'Sales List',
      name: 'salesList',
      desc: '',
      args: [],
    );
  }

  /// `Balance`
  String get balance {
    return Intl.message(
      'Balance',
      name: 'balance',
      desc: '',
      args: [],
    );
  }

  /// `Edit Sales Invoice`
  String get editSalesInvoice {
    return Intl.message(
      'Edit Sales Invoice',
      name: 'editSalesInvoice',
      desc: '',
      args: [],
    );
  }

  /// `Previous Pay Amounts`
  String get previousPayAmounts {
    return Intl.message(
      'Previous Pay Amounts',
      name: 'previousPayAmounts',
      desc: '',
      args: [],
    );
  }

  /// `Return Amount`
  String get returnAmount {
    return Intl.message(
      'Return Amount',
      name: 'returnAmount',
      desc: '',
      args: [],
    );
  }

  /// `FeedBack`
  String get feedBack {
    return Intl.message(
      'FeedBack',
      name: 'feedBack',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get title {
    return Intl.message(
      'Title',
      name: 'title',
      desc: '',
      args: [],
    );
  }

  /// `Enter Your Feedback Tittle`
  String get enterYourFeedBackTitle {
    return Intl.message(
      'Enter Your Feedback Tittle',
      name: 'enterYourFeedBackTitle',
      desc: '',
      args: [],
    );
  }

  /// `Enter your description here`
  String get enterYourDescriptionHere {
    return Intl.message(
      'Enter your description here',
      name: 'enterYourDescriptionHere',
      desc: '',
      args: [],
    );
  }

  /// `Invoice Setting`
  String get invoiceSetting {
    return Intl.message(
      'Invoice Setting',
      name: 'invoiceSetting',
      desc: '',
      args: [],
    );
  }

  /// `Printing Option`
  String get printingOption {
    return Intl.message(
      'Printing Option',
      name: 'printingOption',
      desc: '',
      args: [],
    );
  }

  /// `Logo`
  String get logo {
    return Intl.message(
      'Logo',
      name: 'logo',
      desc: '',
      args: [],
    );
  }

  /// `Save Changes`
  String get saveChanges {
    return Intl.message(
      'Save Changes',
      name: 'saveChanges',
      desc: '',
      args: [],
    );
  }

  /// `Subscription`
  String get subscription {
    return Intl.message(
      'Subscription',
      name: 'subscription',
      desc: '',
      args: [],
    );
  }

  /// `Log Out`
  String get logOUt {
    return Intl.message(
      'Log Out',
      name: 'logOUt',
      desc: '',
      args: [],
    );
  }

  /// `Do not disturb`
  String get doNotDistrub {
    return Intl.message(
      'Do not disturb',
      name: 'doNotDistrub',
      desc: '',
      args: [],
    );
  }

  /// `Contact Us`
  String get contactUs {
    return Intl.message(
      'Contact Us',
      name: 'contactUs',
      desc: '',
      args: [],
    );
  }

  /// `Write your message here`
  String get writeYourMessageHere {
    return Intl.message(
      'Write your message here',
      name: 'writeYourMessageHere',
      desc: '',
      args: [],
    );
  }

  /// `Send Message`
  String get sendMessage {
    return Intl.message(
      'Send Message',
      name: 'sendMessage',
      desc: '',
      args: [],
    );
  }

  /// `Send Your Email`
  String get sendYOurEmail {
    return Intl.message(
      'Send Your Email',
      name: 'sendYOurEmail',
      desc: '',
      args: [],
    );
  }

  /// `Back To Home`
  String get backToHome {
    return Intl.message(
      'Back To Home',
      name: 'backToHome',
      desc: '',
      args: [],
    );
  }

  /// `Select Contacts`
  String get selectContacts {
    return Intl.message(
      'Select Contacts',
      name: 'selectContacts',
      desc: '',
      args: [],
    );
  }

  /// `Message History`
  String get messageHistory {
    return Intl.message(
      'Message History',
      name: 'messageHistory',
      desc: '',
      args: [],
    );
  }

  /// `Transaction`
  String get transaction {
    return Intl.message(
      'Transaction',
      name: 'transaction',
      desc: '',
      args: [],
    );
  }

  /// `No History Found!`
  String get noHistoryFound {
    return Intl.message(
      'No History Found!',
      name: 'noHistoryFound',
      desc: '',
      args: [],
    );
  }

  /// `View details`
  String get viewDetails {
    return Intl.message(
      'View details',
      name: 'viewDetails',
      desc: '',
      args: [],
    );
  }

  /// `No Transaction Found!`
  String get noTransactionFound {
    return Intl.message(
      'No Transaction Found!',
      name: 'noTransactionFound',
      desc: '',
      args: [],
    );
  }

  /// `KYC Verification`
  String get kycVerification {
    return Intl.message(
      'KYC Verification',
      name: 'kycVerification',
      desc: '',
      args: [],
    );
  }

  /// `Identity Verify`
  String get identityVerify {
    return Intl.message(
      'Identity Verify',
      name: 'identityVerify',
      desc: '',
      args: [],
    );
  }

  /// `You need to identity verify before your buying sms`
  String get youNeedToIdentityVerifyBeforeYouBuying {
    return Intl.message(
      'You need to identity verify before your buying sms',
      name: 'youNeedToIdentityVerifyBeforeYouBuying',
      desc: '',
      args: [],
    );
  }

  /// `Government Id`
  String get govermentId {
    return Intl.message(
      'Government Id',
      name: 'govermentId',
      desc: '',
      args: [],
    );
  }

  /// `Take a driver's license, national identity card or passport photo`
  String get takeADriveruser {
    return Intl.message(
      'Take a driver\'s license, national identity card or passport photo',
      name: 'takeADriveruser',
      desc: '',
      args: [],
    );
  }

  /// `Add Document`
  String get addDucument {
    return Intl.message(
      'Add Document',
      name: 'addDucument',
      desc: '',
      args: [],
    );
  }

  /// `History`
  String get history {
    return Intl.message(
      'History',
      name: 'history',
      desc: '',
      args: [],
    );
  }

  /// `Customer`
  String get customer {
    return Intl.message(
      'Customer',
      name: 'customer',
      desc: '',
      args: [],
    );
  }

  /// `Enter Message Content`
  String get enterMessageContent {
    return Intl.message(
      'Enter Message Content',
      name: 'enterMessageContent',
      desc: '',
      args: [],
    );
  }

  /// `Buy Sms`
  String get buySms {
    return Intl.message(
      'Buy Sms',
      name: 'buySms',
      desc: '',
      args: [],
    );
  }

  /// `Complete Transaction`
  String get completeTransaction {
    return Intl.message(
      'Complete Transaction',
      name: 'completeTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Payment Instruction:`
  String get paymentInstructions {
    return Intl.message(
      'Payment Instruction:',
      name: 'paymentInstructions',
      desc: '',
      args: [],
    );
  }

  /// `Payee Name`
  String get payeeName {
    return Intl.message(
      'Payee Name',
      name: 'payeeName',
      desc: '',
      args: [],
    );
  }

  /// `Payee Number`
  String get payeeNumber {
    return Intl.message(
      'Payee Number',
      name: 'payeeNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter your mobile number`
  String get enterYourMobileNumber {
    return Intl.message(
      'Enter your mobile number',
      name: 'enterYourMobileNumber',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Id`
  String get transactionId {
    return Intl.message(
      'Transaction Id',
      name: 'transactionId',
      desc: '',
      args: [],
    );
  }

  /// `Enter your transaction id`
  String get enterYourTransactionId {
    return Intl.message(
      'Enter your transaction id',
      name: 'enterYourTransactionId',
      desc: '',
      args: [],
    );
  }

  /// `Pay with bkash`
  String get payWithBkash {
    return Intl.message(
      'Pay with bkash',
      name: 'payWithBkash',
      desc: '',
      args: [],
    );
  }

  /// `Your message remains`
  String get yourMessageRemains {
    return Intl.message(
      'Your message remains',
      name: 'yourMessageRemains',
      desc: '',
      args: [],
    );
  }

  /// `Sms`
  String get sms {
    return Intl.message(
      'Sms',
      name: 'sms',
      desc: '',
      args: [],
    );
  }

  /// `Add Document Id`
  String get addDocumentId {
    return Intl.message(
      'Add Document Id',
      name: 'addDocumentId',
      desc: '',
      args: [],
    );
  }

  /// `Font Side`
  String get fontSide {
    return Intl.message(
      'Font Side',
      name: 'fontSide',
      desc: '',
      args: [],
    );
  }

  /// `Take an identity card to check your information`
  String get takeaNidCardToCheckYourInformation {
    return Intl.message(
      'Take an identity card to check your information',
      name: 'takeaNidCardToCheckYourInformation',
      desc: '',
      args: [],
    );
  }

  /// `Back side`
  String get backSide {
    return Intl.message(
      'Back side',
      name: 'backSide',
      desc: '',
      args: [],
    );
  }

  /// `Easy to use mobile pos`
  String get easyToUseMobilePos {
    return Intl.message(
      'Easy to use mobile pos',
      name: 'easyToUseMobilePos',
      desc: '',
      args: [],
    );
  }

  /// `AmrDev POS  app is free, easy to use. In fact, it's one of the best  POS systems around the world.`
  String get mobiPosAppIsFree {
    return Intl.message(
      'AmrDev POS  app is free, easy to use. In fact, it\'s one of the best  POS systems around the world.',
      name: 'mobiPosAppIsFree',
      desc: '',
      args: [],
    );
  }

  /// `Choose your features`
  String get choseYourFeature {
    return Intl.message(
      'Choose your features',
      name: 'choseYourFeature',
      desc: '',
      args: [],
    );
  }

  /// `Features are the important part which makes AmrDev POS  different from traditional solutions.`
  String get featureAreTheImportant {
    return Intl.message(
      'Features are the important part which makes AmrDev POS  different from traditional solutions.',
      name: 'featureAreTheImportant',
      desc: '',
      args: [],
    );
  }

  /// `All business solutions`
  String get allBusinessSolution {
    return Intl.message(
      'All business solutions',
      name: 'allBusinessSolution',
      desc: '',
      args: [],
    );
  }

  /// `AmrDev POS  is a complete business solution with stock, account, sales, expense & loss/profit.`
  String get mobiPosIsaCompleteBusinesSolution {
    return Intl.message(
      'AmrDev POS  is a complete business solution with stock, account, sales, expense & loss/profit.',
      name: 'mobiPosIsaCompleteBusinesSolution',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get skip {
    return Intl.message(
      'Skip',
      name: 'skip',
      desc: '',
      args: [],
    );
  }

  /// `Current Stock`
  String get currentStock {
    return Intl.message(
      'Current Stock',
      name: 'currentStock',
      desc: '',
      args: [],
    );
  }

  /// `Total Stocks`
  String get totalStock {
    return Intl.message(
      'Total Stocks',
      name: 'totalStock',
      desc: '',
      args: [],
    );
  }

  /// `Use AmrDev POS`
  String get useMobiPos {
    return Intl.message(
      'Use AmrDev POS',
      name: 'useMobiPos',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message(
      'Next',
      name: 'next',
      desc: '',
      args: [],
    );
  }

  /// `Your Package`
  String get yourPackage {
    return Intl.message(
      'Your Package',
      name: 'yourPackage',
      desc: '',
      args: [],
    );
  }

  /// `Free Plan`
  // String get freePlan {
  //   return Intl.message(
  //     'Free Plan',
  //     name: 'freePlan',
  //     desc: '',
  //     args: [],
  //   );
  // }

  /// `You are using`
  String get youAreUsing {
    return Intl.message(
      'You are using',
      name: 'youAreUsing',
      desc: '',
      args: [],
    );
  }

  /// `Free Package`
  String get freePacakge {
    return Intl.message(
      'Free Package',
      name: 'freePacakge',
      desc: '',
      args: [],
    );
  }

  /// `Premium plan`
  String get premiumPlan {
    return Intl.message(
      'Premium plan',
      name: 'premiumPlan',
      desc: '',
      args: [],
    );
  }

  /// `Package Features`
  String get packageFeatures {
    return Intl.message(
      'Package Features',
      name: 'packageFeatures',
      desc: '',
      args: [],
    );
  }

  /// `For unlimited usages`
  String get forUnlimitedUses {
    return Intl.message(
      'For unlimited usages',
      name: 'forUnlimitedUses',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Premium Plan`
  String get purchasePremiumPlan {
    return Intl.message(
      'Purchase Premium Plan',
      name: 'purchasePremiumPlan',
      desc: '',
      args: [],
    );
  }

  /// `Buy Premium Plan`
  String get buyPremiumPlan {
    return Intl.message(
      'Buy Premium Plan',
      name: 'buyPremiumPlan',
      desc: '',
      args: [],
    );
  }

  /// `Monthly`
  String get monthly {
    return Intl.message(
      'Monthly',
      name: 'monthly',
      desc: '',
      args: [],
    );
  }

  /// `Lifetime\nPurchase`
  String get lifeTimePurchase {
    return Intl.message(
      'Lifetime\nPurchase',
      name: 'lifeTimePurchase',
      desc: '',
      args: [],
    );
  }

  /// `Yearly`
  String get yearly {
    return Intl.message(
      'Yearly',
      name: 'yearly',
      desc: '',
      args: [],
    );
  }

  /// `Pay with Paypal`
  String get payWithPaypal {
    return Intl.message(
      'Pay with Paypal',
      name: 'payWithPaypal',
      desc: '',
      args: [],
    );
  }

  /// `ٍالمخزن with warehouse`
  String get warehouse {
    return Intl.message(
      'Pay with warehouse',
      name: 'warehouse',
      desc: '',
      args: [],
    );
  }

  /// `No Data`
  String get noData {
    return Intl.message(
      'No Data',
      name: 'noData',
      desc: '',
      args: [],
    );
  }

  /// `Your Package Will Expire in 5 Day`
  String get yourPackageWillExpireinDay {
    return Intl.message(
      'Your Package Will Expire in 5 Day',
      name: 'yourPackageWillExpireinDay',
      desc: '',
      args: [],
    );
  }

  /// `Your Package Will Expire Today\n\nPlease Purchase again`
  String get YourPackageWillExpireTodayPleasePurchaseagain {
    return Intl.message(
      'Your Package Will Expire Today\n\nPlease Purchase again',
      name: 'YourPackageWillExpireTodayPleasePurchaseagain',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'af'),
      Locale.fromSubtags(languageCode: 'ar'),
      Locale.fromSubtags(languageCode: 'az'),
      Locale.fromSubtags(languageCode: 'bn'),
      Locale.fromSubtags(languageCode: 'bs'),
      Locale.fromSubtags(languageCode: 'cs'),
      Locale.fromSubtags(languageCode: 'da'),
      Locale.fromSubtags(languageCode: 'de'),
      Locale.fromSubtags(languageCode: 'el'),
      Locale.fromSubtags(languageCode: 'es'),
      Locale.fromSubtags(languageCode: 'fa'),
      Locale.fromSubtags(languageCode: 'fi'),
      Locale.fromSubtags(languageCode: 'fr'),
      Locale.fromSubtags(languageCode: 'he'),
      Locale.fromSubtags(languageCode: 'hi'),
      Locale.fromSubtags(languageCode: 'hr'),
      Locale.fromSubtags(languageCode: 'hu'),
      Locale.fromSubtags(languageCode: 'id'),
      Locale.fromSubtags(languageCode: 'it'),
      Locale.fromSubtags(languageCode: 'ja'),
      Locale.fromSubtags(languageCode: 'kk'),
      Locale.fromSubtags(languageCode: 'km'),
      Locale.fromSubtags(languageCode: 'kn'),
      Locale.fromSubtags(languageCode: 'ko'),
      Locale.fromSubtags(languageCode: 'lo'),
      Locale.fromSubtags(languageCode: 'mr'),
      Locale.fromSubtags(languageCode: 'ms'),
      Locale.fromSubtags(languageCode: 'my'),
      Locale.fromSubtags(languageCode: 'ne'),
      Locale.fromSubtags(languageCode: 'nl'),
      Locale.fromSubtags(languageCode: 'pl'),
      Locale.fromSubtags(languageCode: 'pt'),
      Locale.fromSubtags(languageCode: 'ro'),
      Locale.fromSubtags(languageCode: 'ru'),
      Locale.fromSubtags(languageCode: 'si'),
      Locale.fromSubtags(languageCode: 'sk'),
      Locale.fromSubtags(languageCode: 'sq'),
      Locale.fromSubtags(languageCode: 'sr'),
      Locale.fromSubtags(languageCode: 'sv'),
      Locale.fromSubtags(languageCode: 'sw'),
      Locale.fromSubtags(languageCode: 'ta'),
      Locale.fromSubtags(languageCode: 'th'),
      Locale.fromSubtags(languageCode: 'tr'),
      Locale.fromSubtags(languageCode: 'uk'),
      Locale.fromSubtags(languageCode: 'ur'),
      Locale.fromSubtags(languageCode: 'vi'),
      Locale.fromSubtags(languageCode: 'zh'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}

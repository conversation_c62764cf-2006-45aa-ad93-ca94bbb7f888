// بسم الله الرحمن الرحيم
// فلتر التقرير - مكون يتيح تصفية بيانات التقرير

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/report_model.dart';

/// فلتر التقرير
class ReportFilter extends StatefulWidget {
  /// ينشئ فلتر التقرير
  const ReportFilter({
    super.key,
    required this.onFilterChanged,
    this.initialParameters,
    this.availableCategories,
    this.availableProducts,
    this.availableCustomers,
    this.availableSuppliers,
  });

  /// حدث تغيير الفلتر
  final Function(ReportParameters) onFilterChanged;

  /// معلمات الفلتر الأولية (اختياري)
  final ReportParameters? initialParameters;

  /// الفئات المتاحة (اختياري)
  final List<String>? availableCategories;

  /// المنتجات المتاحة (اختياري)
  final List<String>? availableProducts;

  /// العملاء المتاحون (اختياري)
  final List<String>? availableCustomers;

  /// الموردون المتاحون (اختياري)
  final List<String>? availableSuppliers;

  @override
  State<ReportFilter> createState() => _ReportFilterState();
}

class _ReportFilterState extends State<ReportFilter> {
  late ReportParameters _parameters;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    super.initState();
    _parameters = widget.initialParameters ?? const ReportParameters();
  }

  // تحديث المعلمات وإخطار المستمع
  void _updateParameters(ReportParameters newParameters) {
    setState(() {
      _parameters = newParameters;
    });
    widget.onFilterChanged(_parameters);
  }

  // اختيار تاريخ البداية
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _parameters.startDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _parameters.startDate) {
      _updateParameters(
        ReportParameters(
          startDate: picked,
          endDate: _parameters.endDate,
          categories: _parameters.categories,
          products: _parameters.products,
          customers: _parameters.customers,
          suppliers: _parameters.suppliers,
          includeVat: _parameters.includeVat,
          includeDiscounts: _parameters.includeDiscounts,
          groupBy: _parameters.groupBy,
          sortBy: _parameters.sortBy,
          limit: _parameters.limit,
        ),
      );
    }
  }

  // اختيار تاريخ النهاية
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _parameters.endDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _parameters.endDate) {
      _updateParameters(
        ReportParameters(
          startDate: _parameters.startDate,
          endDate: picked,
          categories: _parameters.categories,
          products: _parameters.products,
          customers: _parameters.customers,
          suppliers: _parameters.suppliers,
          includeVat: _parameters.includeVat,
          includeDiscounts: _parameters.includeDiscounts,
          groupBy: _parameters.groupBy,
          sortBy: _parameters.sortBy,
          limit: _parameters.limit,
        ),
      );
    }
  }

  // تحديث الفئات المحددة
  void _updateSelectedCategories(List<String> selectedCategories) {
    _updateParameters(
      ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: selectedCategories,
        products: _parameters.products,
        customers: _parameters.customers,
        suppliers: _parameters.suppliers,
        includeVat: _parameters.includeVat,
        includeDiscounts: _parameters.includeDiscounts,
        groupBy: _parameters.groupBy,
        sortBy: _parameters.sortBy,
        limit: _parameters.limit,
      ),
    );
  }

  // تحديث المنتجات المحددة
  void _updateSelectedProducts(List<String> selectedProducts) {
    _updateParameters(
      ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: _parameters.categories,
        products: selectedProducts,
        customers: _parameters.customers,
        suppliers: _parameters.suppliers,
        includeVat: _parameters.includeVat,
        includeDiscounts: _parameters.includeDiscounts,
        groupBy: _parameters.groupBy,
        sortBy: _parameters.sortBy,
        limit: _parameters.limit,
      ),
    );
  }

  // تحديث العملاء المحددين
  void _updateSelectedCustomers(List<String> selectedCustomers) {
    _updateParameters(
      ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: _parameters.categories,
        products: _parameters.products,
        customers: selectedCustomers,
        suppliers: _parameters.suppliers,
        includeVat: _parameters.includeVat,
        includeDiscounts: _parameters.includeDiscounts,
        groupBy: _parameters.groupBy,
        sortBy: _parameters.sortBy,
        limit: _parameters.limit,
      ),
    );
  }

  // تحديث الموردين المحددين
  void _updateSelectedSuppliers(List<String> selectedSuppliers) {
    _updateParameters(
      ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: _parameters.categories,
        products: _parameters.products,
        customers: _parameters.customers,
        suppliers: selectedSuppliers,
        includeVat: _parameters.includeVat,
        includeDiscounts: _parameters.includeDiscounts,
        groupBy: _parameters.groupBy,
        sortBy: _parameters.sortBy,
        limit: _parameters.limit,
      ),
    );
  }

  // تحديث تضمين ضريبة القيمة المضافة
  void _updateIncludeVat(bool includeVat) {
    _updateParameters(
      ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: _parameters.categories,
        products: _parameters.products,
        customers: _parameters.customers,
        suppliers: _parameters.suppliers,
        includeVat: includeVat,
        includeDiscounts: _parameters.includeDiscounts,
        groupBy: _parameters.groupBy,
        sortBy: _parameters.sortBy,
        limit: _parameters.limit,
      ),
    );
  }

  // تحديث تضمين الخصومات
  void _updateIncludeDiscounts(bool includeDiscounts) {
    _updateParameters(
      ReportParameters(
        startDate: _parameters.startDate,
        endDate: _parameters.endDate,
        categories: _parameters.categories,
        products: _parameters.products,
        customers: _parameters.customers,
        suppliers: _parameters.suppliers,
        includeVat: _parameters.includeVat,
        includeDiscounts: includeDiscounts,
        groupBy: _parameters.groupBy,
        sortBy: _parameters.sortBy,
        limit: _parameters.limit,
      ),
    );
  }

  // إعادة تعيين الفلتر
  void _resetFilter() {
    _updateParameters(const ReportParameters());
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الفلتر
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'فلتر التقرير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تعيين'),
                  onPressed: _resetFilter,
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),

            // فلتر التاريخ
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('تاريخ البداية'),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () => _selectStartDate(context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _parameters.startDate != null
                                    ? _dateFormat.format(_parameters.startDate!)
                                    : 'اختر التاريخ',
                              ),
                              const Icon(Icons.calendar_today, size: 16),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('تاريخ النهاية'),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () => _selectEndDate(context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _parameters.endDate != null
                                    ? _dateFormat.format(_parameters.endDate!)
                                    : 'اختر التاريخ',
                              ),
                              const Icon(Icons.calendar_today, size: 16),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // فلتر الفئات والمنتجات
            if (widget.availableCategories != null ||
                widget.availableProducts != null) ...[
              Row(
                children: [
                  if (widget.availableCategories != null) ...[
                    Expanded(
                      child: _buildMultiSelect(
                        title: 'الفئات',
                        items: widget.availableCategories!,
                        selectedItems: _parameters.categories,
                        onChanged: _updateSelectedCategories,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (widget.availableProducts != null) ...[
                    Expanded(
                      child: _buildMultiSelect(
                        title: 'المنتجات',
                        items: widget.availableProducts!,
                        selectedItems: _parameters.products,
                        onChanged: _updateSelectedProducts,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
            ],

            // فلتر العملاء والموردين
            if (widget.availableCustomers != null ||
                widget.availableSuppliers != null) ...[
              Row(
                children: [
                  if (widget.availableCustomers != null) ...[
                    Expanded(
                      child: _buildMultiSelect(
                        title: 'العملاء',
                        items: widget.availableCustomers!,
                        selectedItems: _parameters.customers,
                        onChanged: _updateSelectedCustomers,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (widget.availableSuppliers != null) ...[
                    Expanded(
                      child: _buildMultiSelect(
                        title: 'الموردين',
                        items: widget.availableSuppliers!,
                        selectedItems: _parameters.suppliers,
                        onChanged: _updateSelectedSuppliers,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
            ],

            // خيارات إضافية
            Row(
              children: [
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('تضمين ضريبة القيمة المضافة'),
                    value: _parameters.includeVat,
                    onChanged: (value) {
                      if (value != null) {
                        _updateIncludeVat(value);
                      }
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('تضمين الخصومات'),
                    value: _parameters.includeDiscounts,
                    onChanged: (value) {
                      if (value != null) {
                        _updateIncludeDiscounts(value);
                      }
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء قائمة اختيار متعددة
  Widget _buildMultiSelect({
    required String title,
    required List<String> items,
    required List<String> selectedItems,
    required Function(List<String>) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        const SizedBox(height: 8),
        InkWell(
          onTap: () {
            _showMultiSelectDialog(
              context: context,
              title: title,
              items: items,
              selectedItems: selectedItems,
              onChanged: onChanged,
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedItems.isEmpty
                      ? 'اختر $title'
                      : '${selectedItems.length} مختار',
                ),
                const Icon(Icons.arrow_drop_down, size: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // عرض مربع حوار اختيار متعدد
  Future<void> _showMultiSelectDialog({
    required BuildContext context,
    required String title,
    required List<String> items,
    required List<String> selectedItems,
    required Function(List<String>) onChanged,
  }) async {
    final List<String> tempSelectedItems = List.from(selectedItems);

    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('اختر $title'),
          content: StatefulBuilder(
            builder: (context, setState) {
              return SizedBox(
                width: double.maxFinite,
                height: 300,
                child: ListView.builder(
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    final item = items[index];
                    return CheckboxListTile(
                      title: Text(item),
                      value: tempSelectedItems.contains(item),
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            tempSelectedItems.add(item);
                          } else {
                            tempSelectedItems.remove(item);
                          }
                        });
                      },
                    );
                  },
                ),
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                onChanged(tempSelectedItems);
                Navigator.pop(context);
              },
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }
}

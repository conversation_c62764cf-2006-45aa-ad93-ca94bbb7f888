// بسم الله الرحمن الرحيم
// خدمة الإشعارات - توفر واجهة للتعامل مع الإشعارات

import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../core/auth/auth_service.dart';
import '../models/notification_model.dart';

/// خدمة الإشعارات
class NotificationService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة الإشعارات
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // مثيل Firebase Database
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // مؤشر ما إذا كانت الإشعارات مفعلة
  final bool _notificationsEnabled = true;

  // خدمة المصادقة
  final AuthService _authService = AuthService();

  // مولد UUID
  final Uuid _uuid = const Uuid();

  // مراقب الإشعارات
  StreamSubscription<DatabaseEvent>? _notificationsSubscription;

  // الحصول على مرجع قاعدة البيانات للإشعارات
  DatabaseReference _getNotificationsRef(String uid) {
    return _database.ref('features/notifications/$uid');
  }

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // الاستماع للإشعارات الجديدة للمستخدم الحالي
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      _listenForNotifications(currentUser.uid);
    }
  }

  /// إيقاف خدمة الإشعارات
  Future<void> dispose() async {
    await _notificationsSubscription?.cancel();
    _notificationsSubscription = null;
  }

  /// الاستماع للإشعارات الجديدة
  void _listenForNotifications(String uid) {
    _notificationsSubscription?.cancel();
    _notificationsSubscription = _getNotificationsRef(uid)
        .orderByChild('createdAt')
        .startAt(DateTime.now().millisecondsSinceEpoch)
        .onChildAdded
        .listen((event) {
      if (event.snapshot.value != null) {
        final data = event.snapshot.value as Map<dynamic, dynamic>;
        final notification =
            NotificationModel.fromMap(Map<String, dynamic>.from(data));
        _showLocalNotification(notification);
      }
    });
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification(NotificationModel notification) async {
    if (_notificationsEnabled) {
      // استخدام Fluttertoast لعرض إشعار بسيط
      await Fluttertoast.showToast(
        msg: "${notification.title}: ${notification.body}",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.TOP,
        timeInSecForIosWeb: 3,
        backgroundColor: _getNotificationColor(notification.type),
        textColor: Colors.white,
        fontSize: 16.0,
      );

      // يمكن تنفيذ منطق إضافي هنا مثل تشغيل صوت أو اهتزاز
      debugPrint('تم عرض إشعار محلي: ${notification.title}');
    }
  }

  /// الحصول على لون الإشعار حسب نوعه
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return Colors.red;
      case NotificationType.message:
        return Colors.blue;
      case NotificationType.system:
        return Colors.purple;
      case NotificationType.general:
        return Colors.orange;
    }
  }

  /// إرسال إشعار
  Future<bool> sendNotification(NotificationModel notification) async {
    try {
      await _getNotificationsRef(notification.recipientId)
          .child(notification.id)
          .set(notification.toMap());
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار: $e');
      return false;
    }
  }

  /// إنشاء وإرسال إشعار جديد
  Future<bool> createAndSendNotification({
    required String title,
    required String body,
    required NotificationType type,
    required String recipientId,
    NotificationPriority priority = NotificationPriority.medium,
    String? imageUrl,
    String? actionUrl,
    Map<String, dynamic>? data,
    String? senderId,
  }) async {
    try {
      final notification = NotificationModel(
        id: _uuid.v4(),
        title: title,
        body: body,
        type: type,
        status: NotificationStatus.unread,
        priority: priority,
        createdAt: DateTime.now(),
        recipientId: recipientId,
        imageUrl: imageUrl,
        actionUrl: actionUrl,
        data: data,
        senderId: senderId ?? _authService.currentUser?.uid,
      );

      return await sendNotification(notification);
    } catch (e) {
      debugPrint('خطأ في إنشاء وإرسال الإشعار: $e');
      return false;
    }
  }

  /// الحصول على إشعارات المستخدم (مع استبعاد الإشعارات المحذوفة)
  Future<List<NotificationModel>> getUserNotifications(String uid) async {
    try {
      final snapshot = await _getNotificationsRef(uid).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        final notifications = <NotificationModel>[];
        data.forEach((key, value) {
          final notification =
              NotificationModel.fromMap(Map<String, dynamic>.from(value));

          // إضافة الإشعار فقط إذا لم يقم المستخدم بحذفه
          if (!notification.deletedByUsers.contains(uid)) {
            notifications.add(notification);
          }
        });
        // ترتيب الإشعارات حسب تاريخ الإنشاء (الأحدث أولاً)
        notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        return notifications;
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على إشعارات المستخدم: $e');
      return [];
    }
  }

  /// الحصول على إشعارات المستخدم الحالي
  Future<List<NotificationModel>> getCurrentUserNotifications() async {
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      return getUserNotifications(currentUser.uid);
    }
    return [];
  }

  /// الحصول على عدد الإشعارات غير المقروءة للمستخدم الحالي
  Future<int> getUnreadNotificationsCount() async {
    try {
      final notifications = await getCurrentUserNotifications();
      return notifications
          .where((notification) =>
              notification.status == NotificationStatus.unread)
          .length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  /// تحديث حالة الإشعار
  Future<bool> updateNotificationStatus(
      String uid, String notificationId, NotificationStatus status) async {
    try {
      await _getNotificationsRef(uid)
          .child(notificationId)
          .update({'status': status.index});
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الإشعار: $e');
      return false;
    }
  }

  /// حذف إشعار للمستخدم الحالي فقط
  Future<bool> deleteNotification(String uid, String notificationId) async {
    try {
      // الحصول على الإشعار الحالي
      final snapshot =
          await _getNotificationsRef(uid).child(notificationId).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        final notification =
            NotificationModel.fromMap(Map<String, dynamic>.from(data));

        // إضافة المستخدم الحالي إلى قائمة المستخدمين الذين حذفوا الإشعار
        final updatedDeletedByUsers =
            List<String>.from(notification.deletedByUsers);
        if (!updatedDeletedByUsers.contains(uid)) {
          updatedDeletedByUsers.add(uid);
        }

        // تحديث الإشعار في قاعدة البيانات
        await _getNotificationsRef(uid)
            .child(notificationId)
            .update({'deletedByUsers': updatedDeletedByUsers});

        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف الإشعار: $e');
      return false;
    }
  }

  /// حذف جميع إشعارات المستخدم (على مستوى المستخدم فقط)
  Future<bool> deleteAllUserNotifications(String uid) async {
    try {
      // الحصول على جميع الإشعارات
      final snapshot = await _getNotificationsRef(uid).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;

        // تحديث كل إشعار لإضافة المستخدم الحالي إلى قائمة المستخدمين الذين حذفوا الإشعار
        final batch = <Future<void>>[];
        data.forEach((key, value) {
          final notification =
              NotificationModel.fromMap(Map<String, dynamic>.from(value));

          // إضافة المستخدم الحالي إلى قائمة المستخدمين الذين حذفوا الإشعار
          final updatedDeletedByUsers =
              List<String>.from(notification.deletedByUsers);
          if (!updatedDeletedByUsers.contains(uid)) {
            updatedDeletedByUsers.add(uid);

            // إضافة عملية التحديث إلى الدفعة
            batch.add(_getNotificationsRef(uid)
                .child(notification.id)
                .update({'deletedByUsers': updatedDeletedByUsers}));
          }
        });

        // تنفيذ جميع عمليات التحديث
        if (batch.isNotEmpty) {
          await Future.wait(batch);
        }

        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف جميع إشعارات المستخدم: $e');
      return false;
    }
  }

  /// إنشاء إشعار مديونية لمستخدم محدد
  Future<bool> createDebtNotification({
    required String recipientId,
    required String customerName,
    required double amount,
    required DateTime dueDate,
    String? invoiceNumber,
  }) async {
    return createAndSendNotification(
      title: 'تذكير بالمديونية',
      body:
          'لديك مديونية بقيمة $amount جنيه للعميل $customerName تستحق في ${dueDate.day}/${dueDate.month}/${dueDate.year}',
      type: NotificationType.debt,
      recipientId: recipientId,
      priority: NotificationPriority.high,
      data: {
        'customerName': customerName,
        'amount': amount,
        'dueDate': dueDate.millisecondsSinceEpoch,
        'invoiceNumber': invoiceNumber,
      },
    );
  }

  /// إنشاء إشعار مديونية لجميع المستخدمين
  Future<bool> createDebtNotificationForAllUsers({
    required String customerName,
    required double amount,
    required DateTime dueDate,
    required String createdByUserName,
    String? invoiceNumber,
  }) async {
    try {
      // الحصول على قائمة المستخدمين
      final usersSnapshot = await _database.ref('users').get();
      if (!usersSnapshot.exists) {
        return false;
      }

      final usersData = usersSnapshot.value as Map<dynamic, dynamic>;
      final userIds = usersData.keys.cast<String>().toList();

      // إنشاء الإشعار لكل مستخدم
      final batch = <Future<bool>>[];
      for (final userId in userIds) {
        batch.add(createAndSendNotification(
          title: 'إشعار مديونية جديدة',
          body:
              'قام $createdByUserName بإنشاء فاتورة مديونية للعميل $customerName بمبلغ $amount جنيه',
          type: NotificationType.debt,
          recipientId: userId,
          priority: NotificationPriority.high,
          data: {
            'customerName': customerName,
            'amount': amount,
            'dueDate': dueDate.millisecondsSinceEpoch,
            'createdBy': createdByUserName,
            'invoiceNumber': invoiceNumber,
          },
        ));
      }

      // انتظار إكمال جميع العمليات
      final results = await Future.wait(batch);

      // التحقق من نجاح العملية (إذا نجحت عملية واحدة على الأقل)
      return results.any((success) => success);
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار مديونية لجميع المستخدمين: $e');
      return false;
    }
  }

  /// إنشاء إشعار رسالة
  Future<bool> createMessageNotification({
    required String recipientId,
    required String senderName,
    required String messagePreview,
  }) async {
    return createAndSendNotification(
      title: 'رسالة جديدة من $senderName',
      body: messagePreview,
      type: NotificationType.message,
      recipientId: recipientId,
      data: {
        'senderName': senderName,
        'messagePreview': messagePreview,
      },
    );
  }
}

/// مزود خدمة الإشعارات
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

/// مزود إشعارات المستخدم الحالي
final currentUserNotificationsProvider =
    FutureProvider<List<NotificationModel>>((ref) async {
  return await NotificationService().getCurrentUserNotifications();
});

/// مزود عدد الإشعارات غير المقروءة
final unreadNotificationsCountProvider = FutureProvider<int>((ref) async {
  final notifications =
      await NotificationService().getCurrentUserNotifications();
  return notifications
      .where((notification) => notification.status == NotificationStatus.unread)
      .length;
});

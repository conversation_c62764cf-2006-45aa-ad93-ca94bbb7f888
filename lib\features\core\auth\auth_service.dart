// بسم الله الرحمن الرحيم
// خدمة المصادقة - توفر واجهة موحدة للمصادقة سواء بشكل مستقل أو متكامل مع النظام الأصلي

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نموذج بيانات المستخدم
class UserModel {
  final String uid;
  final String? displayName;
  final String? email;
  final String? phoneNumber;
  final String? photoURL;
  final Map<String, dynamic>? additionalData;

  UserModel({
    required this.uid,
    this.displayName,
    this.email,
    this.phoneNumber,
    this.photoURL,
    this.additionalData,
  });

  /// إنشاء نموذج من بيانات Firebase User
  factory UserModel.fromFirebaseUser(User user,
      {Map<String, dynamic>? additionalData}) {
    return UserModel(
      uid: user.uid,
      displayName: user.displayName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      photoURL: user.photoURL,
      additionalData: additionalData,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'displayName': displayName,
      'email': email,
      'phoneNumber': phoneNumber,
      'photoURL': photoURL,
      'additionalData': additionalData,
    };
  }

  /// إنشاء نموذج من Map
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      displayName: map['displayName'],
      email: map['email'],
      phoneNumber: map['phoneNumber'],
      photoURL: map['photoURL'],
      additionalData: map['additionalData'],
    );
  }
}

/// وضع المصادقة - مستقل أو متكامل
enum AuthMode {
  /// وضع مستقل - يستخدم نظام مصادقة خاص
  standalone,

  /// وضع متكامل - يستخدم نظام المصادقة الأصلي
  integrated,
}

/// خدمة المصادقة
class AuthService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة المصادقة
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // مثيل Firebase Auth
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // وضع المصادقة الحالي - يستخدم لتحديد طريقة المصادقة (مستقل أو متكامل مع النظام الأصلي)
  final AuthMode _authMode = AuthMode.standalone;

  // الحصول على وضع المصادقة الحالي
  AuthMode getAuthMode() => _authMode;

  // المستخدم الحالي
  UserModel? _currentUser;

  // الحصول على المستخدم الحالي
  UserModel? get currentUser {
    // التحقق من وجود مستخدم حالي في Firebase أولاً
    final firebaseUser = _auth.currentUser;
    if (firebaseUser != null) {
      return UserModel.fromFirebaseUser(firebaseUser);
    }
    // إذا لم يكن هناك مستخدم في Firebase، استخدم المستخدم المخزن محليًا
    return _currentUser;
  }

  /// تهيئة خدمة المصادقة
  Future<void> initialize() async {
    // التحقق من وجود مستخدم حالي في Firebase
    final firebaseUser = _auth.currentUser;
    if (firebaseUser != null) {
      _currentUser = UserModel.fromFirebaseUser(firebaseUser);
    } else {
      // التحقق من وجود مستخدم محفوظ محليًا
      await _loadUserFromLocal();
    }
  }

  /// تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserModel?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user != null) {
        _currentUser = UserModel.fromFirebaseUser(userCredential.user!);
        await _saveUserToLocal(_currentUser!);
        return _currentUser;
      }
    } catch (e) {
      debugPrint('خطأ في تسجيل الدخول: $e');
      rethrow;
    }
    return null;
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    await _auth.signOut();
    _currentUser = null;
    await _clearLocalUser();
  }

  /// حفظ بيانات المستخدم محليًا
  Future<void> _saveUserToLocal(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_data', user.toMap().toString());
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات المستخدم محليًا: $e');
    }
  }

  /// تحميل بيانات المستخدم من التخزين المحلي
  Future<void> _loadUserFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('user_data');
      if (userData != null && userData.isNotEmpty) {
        // تحويل النص إلى Map
        final Map<String, dynamic> userMap =
            {}; // تحتاج إلى تنفيذ التحويل من النص
        _currentUser = UserModel.fromMap(userMap);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المستخدم من التخزين المحلي: $e');
    }
  }

  /// مسح بيانات المستخدم من التخزين المحلي
  Future<void> _clearLocalUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_data');
    } catch (e) {
      debugPrint('خطأ في مسح بيانات المستخدم من التخزين المحلي: $e');
    }
  }
}

/// مزود خدمة المصادقة
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// مزود حالة المستخدم الحالي
final currentUserProvider = StateProvider<UserModel?>((ref) {
  return AuthService().currentUser;
});

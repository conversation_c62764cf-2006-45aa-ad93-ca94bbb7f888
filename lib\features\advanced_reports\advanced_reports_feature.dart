// بسم الله الرحمن الرحيم
// ميزة التقارير المتقدمة - تنفيذ واجهة الميزة للتقارير المتقدمة

import 'package:flutter/material.dart';
import '../core/feature_interface.dart';
import 'screens/reports_home_screen.dart';
import 'screens/sales_report_screen.dart';
import 'screens/inventory_report_screen.dart';
import 'screens/financial_report_screen.dart';
import 'screens/custom_report_screen.dart';

/// ميزة التقارير المتقدمة
class AdvancedReportsFeature implements FeatureInterface {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من ميزة التقارير المتقدمة
  static final AdvancedReportsFeature _instance = AdvancedReportsFeature._internal();
  factory AdvancedReportsFeature() => _instance;
  AdvancedReportsFeature._internal();

  // حالة تفعيل الميزة
  bool _isEnabled = true;

  @override
  String get featureName => 'التقارير المتقدمة';

  @override
  String get featureDescription => 'تقارير متقدمة للمبيعات والمخزون والمالية';

  @override
  IconData get featureIcon => Icons.bar_chart;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Widget getMainScreen() {
    return const ReportsHomeScreen();
  }

  @override
  Map<String, WidgetBuilder> getRoutes() {
    return {
      ReportsHomeScreen.routeName: (context) => const ReportsHomeScreen(),
      '/reports/sales': (context) => const SalesReportScreen(),
      '/reports/inventory': (context) => const InventoryReportScreen(),
      '/reports/financial': (context) => const FinancialReportScreen(),
      '/reports/custom': (context) => const CustomReportScreen(),
    };
  }

  @override
  Future<void> initialize() async {
    // تهيئة ميزة التقارير المتقدمة
    // في الإصدار الحقيقي، يمكن تهيئة الخدمات والموارد هنا
  }

  @override
  Future<void> dispose() async {
    // تنظيف موارد ميزة التقارير المتقدمة
    // في الإصدار الحقيقي، يمكن تنظيف الموارد هنا
  }
}
